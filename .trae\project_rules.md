# Project Rules — Execução Contínua (seguir até concluir)

## Objetivo
Concluir tarefas com o **mínimo de interações** do usuário, dividindo o trabalho em etapas que caibam no limite de raciocínio e **retomando automaticamente** quando necessário.

## Modo de Trabalho
- **Planejamento leve e incremental**: antes de começar, crie um micro-plano com 3–7 etapas objetivas.
- **Chunking obrigatório**: cada etapa deve ser pequena o bastante para terminar sem exceder o limite de raciocínio.
- **Execução sem pedir confirmação**: execute as etapas em sequência **sem solicitar “OK”** entre elas, exceto em casos de risco/custos.

## Política de Continuidade
- Se atingir qualquer limite (token/raciocínio) **no meio de uma etapa**, **retome automaticamente** na próxima mensagem com:
  1) breve resumo do que já foi feito,
  2) o **próximo sub-passo** da etapa atual,
  3) e continue a execução.
- **Não reprojete** o plano inteiro a cada retomada; apenas continue de onde parou.

## Critérios para Interromper e Perguntar
Pergunte ao usuário **somente** quando:
- A ação é **irreversível** (ex.: deletar dados, custos em conta real, publicar para produção).
- Há **ambiguidade material** que possa levar a retrabalho.
- Surge uma **falha externa** que bloqueia (ex.: credenciais inválidas).

## Telemetria de Progresso
- Ao fim de cada etapa, atualize um arquivo leve no workspace chamado `trae_progress.md` com:
  - [x] Etapa concluída
  - [ ] Próxima etapa
  - Notas rápidas de decisões/paths criados

## Limites e Orçamento
- **Auto-continuidade**: permitido encadear até **30 mensagens** de retomada automática sem interação do usuário.
- **Tempo máximo por etapa**: 3–5 minutos de processamento.
- Se esgotar o orçamento acima, faça um **resumo curto** e **pare**, pedindo ao usuário “Deseja que eu continue?”.

## Estilo e Padrões (Projeto Excalibur/Deriv)
- Siga SOLID, módulos coesos, tipos fortes.
- Crie logs visíveis em `logs/` e mensagens claras no console/terminal da IDE.
- Em tarefas longas (ex.: scaffolding + UI + conexão Deriv), priorize:
  1. setup mínimo funcional,
  2. conexão + ping + saldo,
  3. reconexão automática,
  4. depois incrementos visuais.

## Segurança
- Em **contas reais** da Deriv: simular primeiro; confirmar explicitamente antes de operações com dinheiro real.
- Nunca expor tokens/chaves em repositório.

