{
  // 🔐 Desativa o sistema de confiança do workspace
  "security.workspace.trust.enabled": false,

  // 🧪 Permite execução de scripts PowerShell sem confirmação
  "code-runner.executorMap": {
    "powershell": "powershell -ExecutionPolicy ByPass -File"
  },

  // ⚡ Executa scripts no terminal (evita prompts do Code Runner)
  "code-runner.runInTerminal": true,
  "code-runner.confirmClearOutput": false,

  // 🧼 Não pede confirmação ao fechar terminal com processos ativos
  "terminal.integrated.confirmOnExit": "never",

  // 🧭 Evita prompts ao abrir arquivos externos
  "window.confirmBeforeClose": "never",

  // 🧹 Remove mensagens de confirmação ao salvar arquivos
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 1000,

  // 🧩 Evita prompts ao instalar extensões
  "extensions.autoUpdate": true,
  "extensions.ignoreRecommendations": true
}
