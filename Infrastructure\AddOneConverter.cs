using System;
using System.Globalization;
using System.Windows.Data;

namespace Excalibur.Infrastructure
{
    public class AddOneConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int intValue)
            {
                return intValue; // Removido +1 para começar do nível 0
            }
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int intValue)
            {
                return intValue; // Removido -1 para manter consistência
            }
            return value;
        }
    }
}