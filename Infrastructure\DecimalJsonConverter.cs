using System;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Excalibur.Infrastructure
{
    /// <summary>
    /// Conversor JSON customizado para decimais que garante no máximo 2 casas decimais
    /// </summary>
    public class DecimalJsonConverter : JsonConverter<decimal>
    {
        public override decimal Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                var stringValue = reader.GetString();
                if (decimal.TryParse(stringValue, NumberStyles.Float, CultureInfo.InvariantCulture, out decimal result))
                {
                    return result;
                }
            }
            else if (reader.TokenType == JsonTokenType.Number)
            {
                return reader.GetDecimal();
            }
            
            return 0m;
        }

        public override void Write(Utf8JsonWriter writer, decimal value, JsonSerializerOptions options)
        {
            // Arredondar para 2 casas decimais e escrever como número
            var roundedValue = Math.Round(value, 2);
            writer.WriteNumberValue(roundedValue);
        }
    }
}