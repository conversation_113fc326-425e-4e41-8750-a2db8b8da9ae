using System;
using System.Globalization;
using System.Windows.Data;

namespace Excalibur.Infrastructure;

public class ProfitColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is decimal decimalValue)
        {
            return decimalValue >= 0 ? "#FF2ECC71" : "#FFFF4444";
        }
        
        if (value is double doubleValue)
        {
            return doubleValue >= 0 ? "#FF2ECC71" : "#FFFF4444";
        }
        
        return "White";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
