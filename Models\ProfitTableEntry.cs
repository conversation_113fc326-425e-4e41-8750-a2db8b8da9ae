using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Excalibur.Models
{
    public class ProfitTableEntry : INotifyPropertyChanged
    {
        private string _refId = string.Empty;
        private string _contract = string.Empty;
        private string _duration = string.Empty;
        private DateTime? _entrySpot;
        private DateTime? _exitSpot;
        private decimal _stake;
        private decimal _totalProfitLoss;
        private bool _isActive = true;
        private decimal _currentPrice;
        private decimal _payout;
        private decimal? _entryPrice;
        private decimal? _exitPrice;
        private int _sessionId = 1; // Identificador da sessão para calcular SessionProfit corretamente
        private bool _isDual; // Indica se a entrada pertence a uma operação Dual
        private bool _isAccountedInSession = false; // Controla se já foi contabilizado no SessionProfit

        public string RefId
        {
            get => _refId;
            set { _refId = value; OnPropertyChanged(); }
        }

        public string Contract
        {
            get => _contract;
            set { _contract = value; OnPropertyChanged(); }
        }

        public string Duration
        {
            get => _duration;
            set { _duration = value; OnPropertyChanged(); }
        }

        public DateTime? EntrySpot
        {
            get => _entrySpot;
            set { _entrySpot = value; OnPropertyChanged(); OnPropertyChanged(nameof(EntrySpotDisplay)); }
        }

        public DateTime? ExitSpot
        {
            get => _exitSpot;
            set { _exitSpot = value; OnPropertyChanged(); OnPropertyChanged(nameof(ExitSpotDisplay)); }
        }

        public decimal Stake
        {
            get => _stake;
            set { _stake = value; OnPropertyChanged(); OnPropertyChanged(nameof(StakeDisplay)); }
        }

        public decimal TotalProfitLoss
        {
            get => _totalProfitLoss;
            set 
            { 
                // DEBUG: Log when TotalProfitLoss is being set
                if (value == -0.04m || value == 0.04m)
                {
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] TotalProfitLoss set to {value:F2} for contract {RefId}");
                    Console.WriteLine($"[DEBUG] TotalProfitLoss set to {value:F2} for contract {RefId}");
                }
                
                _totalProfitLoss = value; 
                OnPropertyChanged(); 
                OnPropertyChanged(nameof(TotalProfitLossDisplay));
                OnPropertyChanged(nameof(ProfitLossColor));
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set { _isActive = value; OnPropertyChanged(); }
        }

        public decimal CurrentPrice
        {
            get => _currentPrice;
            set { _currentPrice = value; OnPropertyChanged(); UpdateProfitLoss(); }
        }

        public decimal Payout
        {
            get => _payout;
            set { _payout = value; OnPropertyChanged(); }
        }

        public decimal? EntryPrice
        {
            get => _entryPrice;
            set { _entryPrice = value; OnPropertyChanged(); OnPropertyChanged(nameof(EntrySpotDisplay)); }
        }

        public decimal? ExitPrice
        {
            get => _exitPrice;
            set { _exitPrice = value; OnPropertyChanged(); OnPropertyChanged(nameof(ExitSpotDisplay)); }
        }

        public int SessionId
        {
            get => _sessionId;
            set { _sessionId = value; OnPropertyChanged(); }
        }

        public bool IsDual
        {
            get => _isDual;
            set { _isDual = value; OnPropertyChanged(); }
        }

        public bool IsAccountedInSession
        {
            get => _isAccountedInSession;
            set { _isAccountedInSession = value; OnPropertyChanged(); }
        }

        // Display properties: show the API spot price with exactly 3 decimal places.
        // When the API time is available, append it discreetly in parentheses (HH:mm:ss).
        // If the API has not provided the price yet, display '---'.
        public string EntrySpotDisplay
        {
            get
            {
                if (!EntryPrice.HasValue) return "---";
                var price = EntryPrice.Value.ToString("F3");
                if (EntrySpot.HasValue)
                {
                    // Use UTC time from the API timestamp and show only time portion
                    return $"{price} ({EntrySpot.Value.ToUniversalTime():HH:mm:ss})";
                }
                return price;
            }
        }

        public string ExitSpotDisplay
        {
            get
            {
                if (!ExitPrice.HasValue) return "---";
                var price = ExitPrice.Value.ToString("F3");
                if (ExitSpot.HasValue)
                {
                    return $"{price} ({ExitSpot.Value.ToUniversalTime():HH:mm:ss})";
                }
                return price;
            }
        }
        public string StakeDisplay => Stake.ToString("F2");
        public string TotalProfitLossDisplay => TotalProfitLoss.ToString("F2");
        public string ProfitLossColor => TotalProfitLoss >= 0 ? "#FF2ECC71" : "#FFFF4444";

        private void UpdateProfitLoss()
        {
            if (IsActive && CurrentPrice > 0 && Payout > 0)
            {
                // For binary options, the profit/loss calculation is different
                // We'll use a simplified approach here - the real calculation will come from the API
                // This is just for visual feedback during active contracts
                var priceMovement = CurrentPrice - EntryPrice;
                var estimatedProfit = priceMovement > 0 ? (Payout - Stake) : -Stake;

                // DEBUG: Log the calculation for -0.04 values
                if (estimatedProfit == -0.04m || Math.Abs(estimatedProfit - (-0.04m)) < 0.001m)
                {
                    Console.WriteLine($"[DEBUG UpdateProfitLoss] Contract {RefId}: CurrentPrice={CurrentPrice:F5}, EntryPrice={EntryPrice:F5}, Payout={Payout:F2}, Stake={Stake:F2}");
                    Console.WriteLine($"[DEBUG UpdateProfitLoss] priceMovement={priceMovement:F5}, estimatedProfit={estimatedProfit:F2}");
                }

                // Only update if we don't have the final result yet
                if (IsActive)
                {
                    TotalProfitLoss = estimatedProfit;
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
