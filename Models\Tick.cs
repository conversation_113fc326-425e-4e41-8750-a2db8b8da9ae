using System;

namespace Excalibur.Models;

public class Tick
{
    public string Symbol { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public DateTime Timestamp { get; set; }
    public long Epoch { get; set; }
    
    public Tick()
    {
    }
    
    public Tick(string symbol, decimal price, DateTime timestamp, long epoch)
    {
        Symbol = symbol;
        Price = price;
        Timestamp = timestamp;
        Epoch = epoch;
    }
}
