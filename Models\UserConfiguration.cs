using System.Text.Json.Serialization;

namespace Excalibur.Models
{
    /// <summary>
    /// Modelo para salvar/carregar configurações do usuário no local storage
    /// </summary>
    public class UserConfiguration
    {
        // Configurações de stake
        public decimal Stake { get; set; } = 0.35m;
        public decimal DualTakeProfit { get; set; } = 0.03m;
        public int DualLevel { get; set; } = 5;
        public int DualSession { get; set; } = 1;

        // Configurações do Novo Modo Dual
        public decimal DualLucroAlvo { get; set; } = 1.00m;
        public decimal DualAlfa { get; set; } = 0.35m; // α - % do prejuízo a recuperar por parcela
        public decimal DualLucroBase { get; set; } = 0.04m;
        public decimal DualP { get; set; } = 1.90m; // Agora representa a razão R = y/x
        public decimal DualMaxLossAmount { get; set; } = 10.00m;
        
        // Configurações de Money Management - Martingale
        public decimal MartingaleFactor { get; set; } = 2.0m;
        public decimal InitialStakeAmount { get; set; } = 0.35m;
        public int MartingaleLevel { get; set; } = 3;
        public decimal MaxLossAmount { get; set; } = 10.0m;

        // Seleções de mercado
        public string? SelectedMarket { get; set; }
        public string? SelectedSubMarket { get; set; }
        public string? SelectedActiveSymbol { get; set; }
        public string? SelectedContractType { get; set; }
        public string? SelectedDualContractType { get; set; }

        // Parâmetros do contrato
        public int DurationValue { get; set; } = 1;
        public string DurationUnit { get; set; } = "Ticks";

        // Estratégias ativadas
        public bool IsMartingaleEnabled { get; set; } = false;
        public bool IsDualEnabled { get; set; } = false;
        public bool IsFastMartingale { get; set; } = false;

        // Modo Dual
        public bool IsDualSafeMode { get; set; } = false;

        // Timestamp da última configuração
        public DateTime LastSaved { get; set; } = DateTime.Now;

        /// <summary>
        /// Valida se a configuração contém dados válidos
        /// </summary>
        public bool IsValid()
        {
            return Stake >= 0.35m && 
                   DualTakeProfit >= 0.01m && 
                   DualLevel > 0 &&
                   DurationValue > 0 &&
                   MartingaleFactor >= 1.1m &&
                   InitialStakeAmount >= 0.35m &&
                   MartingaleLevel > 0 &&
                   MaxLossAmount > 0 &&
                   !string.IsNullOrEmpty(DurationUnit);
        }

        /// <summary>
        /// Cria uma configuração padrão
        /// </summary>
        public static UserConfiguration CreateDefault()
        {
            return new UserConfiguration
            {
                Stake = 0.35m,
                DualTakeProfit = 0.03m,
                DualLevel = 5,
                DualSession = 1,
                DualLucroAlvo = 1.00m,
                DualAlfa = 0.35m,
                DualLucroBase = 0.04m,
                DualP = 1.90m,
                DualMaxLossAmount = 10.00m,
                MartingaleFactor = 2.0m,
                InitialStakeAmount = 0.35m,
                MartingaleLevel = 3,
                MaxLossAmount = 10.0m,
                DurationValue = 1,
                DurationUnit = "Ticks",
                LastSaved = DateTime.Now
            };
        }
    }
}
