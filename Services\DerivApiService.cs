using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Websocket.Client;
using Excalibur.Models;

namespace Excalibur.Services;

// Thread-safe HashSet implementation
public class ConcurrentHashSet<T> : IDisposable
{
    private readonly HashSet<T> _hashSet = new HashSet<T>();
    private readonly ReaderWriterLockSlim _lock = new ReaderWriterLockSlim();

    public bool Add(T item)
    {
        _lock.EnterWriteLock();
        try
        {
            return _hashSet.Add(item);
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }

    public bool Contains(T item)
    {
        _lock.EnterReadLock();
        try
        {
            return _hashSet.Contains(item);
        }
        finally
        {
            _lock.ExitReadLock();
        }
    }

    public void Dispose()
    {
        _lock?.Dispose();
    }
}

public class DerivApiService : IDerivApiService
{
    private readonly ILogger<DerivApiService> _logger;
    private IWebsocketClient? _ws;
    private Timer? _pingTimer;
    private readonly Stopwatch _pingStopwatch = new();
    private DateTime _lastPongReceived = DateTime.UtcNow;
    private Timer? _connectionMonitorTimer;
    private Timer? _balanceSyncTimer; // Timer para sincronização frequente de saldo
    private readonly SemaphoreSlim _reconnectGate = new(1, 1);
    private volatile bool _isReconnecting = false;
    private volatile bool _isAuthorizing = false;
    private volatile bool _isAuthorized = false;
    private DateTime _lastReconnectAtUtc = DateTime.MinValue;
    private bool _lastIsConnectedState = false; // Para rastrear mudanças no IsConnected

    // Dicionário para rastrear requisições e suas respostas
    private readonly ConcurrentDictionary<int, TaskCompletionSource<JsonElement>> _pendingRequests = new();
    private int _nextRequestId = 1;
    private readonly ConcurrentHashSet<string> _processedContracts = new ConcurrentHashSet<string>();
    private readonly object _contractProcessingLock = new object();

    // Proposal subscription caches for ultra-low latency buys
    private readonly Dictionary<string, ProposalResponse> _proposalStreamCache = new(); // key -> latest proposal
    private readonly Dictionary<string, string> _proposalStreamKeyBySubId = new();     // subscriptionId -> key
    private readonly Dictionary<string, string> _proposalStreamSubIdByKey = new();     // key -> subscriptionId

    // Configurações - virão da UI no futuro
    private string _apiToken = "oLJLFtINRDBGUh1";
    private int _appId = 82663;

    public bool IsConnected => _ws?.IsRunning == true && _isAuthorized;

    public event Action<string, string, double>? AccountInfoUpdated;
    public event Action<long>? PingUpdated;
    public event Action? ConnectionEstablished;
    public event Action? ConnectionLost;
    public event Action<bool>? IsConnectedChanged; // Notifica mudanças no status IsConnected
    public event Action<bool>? ContractResult; // true = win, false = loss
    public event Action<string>? ContractNearExpiry; // contract_id when contract is near expiry
    public event Action<decimal, DateTime>? TickReceived; // price, timestamp for ticks stream
    public event Action<string, decimal, decimal, DateTime>? ContractFinished; // contractId, profit, exitPrice, exitTime
    public event Action<string, string, string, decimal, decimal, DateTime>? ContractPurchased; // contractId, contractType, duration, stake, payout, purchaseTime
    public event Action<string, decimal, long>? ContractEntryTickReceived; // contractId, entry_tick, entry_tick_time
    
    private string? _currentTicksSubscription; // Track current ticks subscription

    public DerivApiService(ILogger<DerivApiService> logger)
    {
        _logger = logger;
        InitializeClient();
    }

    private void InitializeClient()
    {
        var url = new Uri($"wss://ws.binaryws.com/websockets/v3?app_id={_appId}");
        _ws = new WebsocketClient(url);

        _ws.ReconnectTimeout = TimeSpan.FromSeconds(30); // Tempo para tentar reconectar
        _ws.ErrorReconnectTimeout = TimeSpan.FromSeconds(30);

        _ws.ReconnectionHappened.Subscribe(info => {
            _logger.LogInformation($"Reconexão bem-sucedida: {info.Type}");
            // Garante um único fluxo de pós-reconexão
            Task.Run(async () => await HandleReconnectedAsync());
        });

        _ws.DisconnectionHappened.Subscribe(info => {
            _logger.LogWarning($"Conexão perdida: {info.Type}");
            // Clean up all timers on disconnection
            _pingTimer?.Dispose();
            _connectionMonitorTimer?.Dispose();
            _balanceSyncTimer?.Dispose();
            // Reset flags
            _isAuthorized = false;
            _isAuthorizing = false;
            // Fail fast all pending requests to evitar travas/timeout em cadeia
            foreach (var kvp in _pendingRequests.ToArray())
            {
                if (_pendingRequests.TryRemove(kvp.Key, out var tcs))
                {
                    tcs.TrySetException(new Exception("Conexão perdida durante a requisição."));
                }
            }
            CheckAndNotifyConnectionStatusChange();
            ConnectionLost?.Invoke();
        });

        _ws.MessageReceived.Subscribe(msg => {
            // Removed verbose message logging for performance optimization
            var text = msg.Text;
            if (string.IsNullOrEmpty(text)) return;
            ProcessGeneralMessage(text);
        });
    }

    private void CheckAndNotifyConnectionStatusChange()
    {
        var currentState = IsConnected;
        if (currentState != _lastIsConnectedState)
        {
            _lastIsConnectedState = currentState;
            _logger.LogInformation($"Status de conexão alterado para: {currentState}");
            IsConnectedChanged?.Invoke(currentState);
        }
    }

    public async Task ConnectAndAuthorizeAsync()
    {
        _logger.LogInformation("Conectando à API Deriv...");
        if (_ws == null)
        {
            _logger.LogError("WebSocket client is not initialized.");
            return;
        }

        await _ws.Start();
        if (_ws.IsRunning)
        {
            await SafeAuthorizeAsync();
        }
    }

    private Task SafeAuthorizeAsync()
    {
        if (_ws == null) return Task.CompletedTask;
        if (_isAuthorizing || _isAuthorized) return Task.CompletedTask;
        _isAuthorizing = true;
        try
        {
            var authRequest = new { authorize = _apiToken };
            _ws.Send(JsonSerializer.Serialize(authRequest));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar autorização");
            _isAuthorizing = false;
        }
        return Task.CompletedTask;
    }
    
    public async Task SubscribeToBalanceAsync()
    {
        var request = new { balance = 1, subscribe = 1 };
        await SendRequestAsync(request);
        // Reduced logging for performance
    }
    
    private void StartPingTimer()
    {
        // Dispose timers anteriores para evitar múltiplas instâncias
        _pingTimer?.Dispose();
        _connectionMonitorTimer?.Dispose();
        // Heartbeat: ping a cada 2s
        _pingTimer = new Timer(_ => SendPing(), null, 0, 2000);
        
        // Monitor de conexão: verifica a cada 3s
        _connectionMonitorTimer = new Timer(_ => CheckConnectionHealth(), null, 3000, 3000);
        _lastPongReceived = DateTime.UtcNow;
    }
    
    private void StartBalanceSyncTimer()
    {
        // Dispose timer anterior para evitar múltiplas instâncias
        _balanceSyncTimer?.Dispose();
        // Sincronizar saldo a cada 10 segundos para manter dados atualizados
        _balanceSyncTimer = new Timer(async _ => await SyncBalanceAndPortfolioAsync(), null, 
            TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));
    }
    
    private async Task SyncBalanceAndPortfolioAsync()
    {
        try
        {
            if (!_isAuthorized || _ws?.IsRunning != true)
                return;
                
            // Solicitar atualização de saldo
            await RequestBalanceUpdate();
            
            // Solicitar atualização de portfolio
            await RequestPortfolioUpdate();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante sincronização automática de saldo e portfolio");
        }
    }
    
    private Task RequestBalanceUpdate()
    {
        try
        {
            var balanceRequest = new
            {
                balance = 1,
                req_id = GetNextRequestId()
            };
            
            var json = JsonSerializer.Serialize(balanceRequest);
            if (_ws != null)
            {
                _ws.Send(json);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao solicitar atualização de saldo");
        }
        return Task.CompletedTask;
    }
    
    private Task RequestPortfolioUpdate()
    {
        try
        {
            var portfolioRequest = new
            {
                portfolio = 1,
                req_id = GetNextRequestId()
            };
            
            var json = JsonSerializer.Serialize(portfolioRequest);
            if (_ws != null)
            {
                _ws.Send(json);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao solicitar atualização de portfolio");
        }
        return Task.CompletedTask;
    }
    
    private int GetNextRequestId()
    {
        return Interlocked.Increment(ref _nextRequestId);
    }

    private void SendPing()
    {
        try
        {
            if (_ws?.IsRunning == true)
            {
                _pingStopwatch.Restart();
                var pingRequest = new { ping = 1 };
                _ws.Send(JsonSerializer.Serialize(pingRequest));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao enviar ping - conexão pode estar instável");
        }
    }
    
    private void CheckConnectionHealth()
    {
        try
        {
            var timeSinceLastPong = DateTime.UtcNow - _lastPongReceived;
            
            // More tolerant timeout: If no pong received in 5 seconds, consider connection unhealthy
            // This reduces false positives that cause unnecessary reconnections
            if (timeSinceLastPong.TotalSeconds > 5.0 && _ws?.IsRunning == true)
            {
                _ = Task.Run(async () => await TryDebouncedReconnectAsync(timeSinceLastPong));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no monitoramento de saúde da conexão");
        }
    }

    private async Task TryDebouncedReconnectAsync(TimeSpan unhealthyFor)
    {
        if (_ws == null) return;
        var now = DateTime.UtcNow;
        // Debounce: evite cascata de reconexões
        if (now - _lastReconnectAtUtc < TimeSpan.FromSeconds(10)) return;
        if (_isReconnecting) return;

        _logger.LogWarning($"Conexão não responsiva há {unhealthyFor.TotalSeconds:F1}s - iniciando reconexão segura");
        _isReconnecting = true;
        await _reconnectGate.WaitAsync();
        try
        {
            _lastReconnectAtUtc = DateTime.UtcNow;
            // Parar timers antes de reiniciar
            _pingTimer?.Dispose();
            _connectionMonitorTimer?.Dispose();

            try { await _ws.Stop(WebSocketCloseStatus.NormalClosure, "Connection health check failed"); }
            catch { /* ignore */ }

            await Task.Delay(800);
            await _ws.Start();
            await SafeAuthorizeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante reconexão segura");
        }
        finally
        {
            _isReconnecting = false;
            _reconnectGate.Release();
        }
    }

    private async Task HandleReconnectedAsync()
    {
        try
        {
            await _reconnectGate.WaitAsync();
            _isReconnecting = false; // reconectado pelo cliente
            _lastReconnectAtUtc = DateTime.UtcNow;
        }
        finally
        {
            _reconnectGate.Release();
        }
        await SafeAuthorizeAsync();
    }

    // Novo método para processar QUALQUER mensagem da API
    private void ProcessGeneralMessage(string jsonMessage)
    {
        try
        {
            using var doc = JsonDocument.Parse(jsonMessage);
            var root = doc.RootElement;

            // ULTRA-PRIORITY processing for contract-related messages - handle immediately
            if (root.TryGetProperty("msg_type", out var msgTypeElement))
            {
                var msgType = msgTypeElement.GetString();

                // Handle proposal subscription streams (subscribe=1)
                if (msgType == "proposal")
                {
                    try
                    {
                        // Capture subscription id
                        if (root.TryGetProperty("subscription", out var subElem) &&
                            subElem.TryGetProperty("id", out var idElem))
                        {
                            var subId = idElem.GetString() ?? string.Empty;
                            // Build proposal response snapshot
                            var proposalResponse = root.Deserialize<ProposalResponse>();
                            if (!string.IsNullOrEmpty(subId) && proposalResponse?.Proposal != null)
                            {
                                if (_proposalStreamKeyBySubId.TryGetValue(subId, out var key) && !string.IsNullOrEmpty(key))
                                {
                                    lock (_proposalStreamCache)
                                    {
                                        _proposalStreamCache[key] = proposalResponse;
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[PROPOSAL STREAM] Failed to process proposal update");
                    }
                }
                
                // INSTANT PRIORITY: Contract messages bypass all other processing
                if (msgType == "proposal_open_contract")
                {
                    // Process contract updates with absolute priority for immediate loss detection
                    ProcessMessage(jsonMessage);
                    return;
                }
                else if (msgType == "portfolio")
                {
                    // Process portfolio updates with high priority for fallback loss detection
                    ProcessMessage(jsonMessage);
                    return;
                }
            }

            // Verifica se a mensagem é uma resposta a uma de nossas requisições
            if (root.TryGetProperty("req_id", out var reqIdElement) && 
                _pendingRequests.TryRemove(reqIdElement.GetInt32(), out var tcs))
            {
                // Se for, completa a Task com o resultado
                if (root.TryGetProperty("error", out var error))
                {
                    tcs.SetException(new Exception(error.GetProperty("message").GetString()));
                }
                else
                {
                    tcs.SetResult(root.Clone());
                }
                return;
            }

            // Processa mensagens que não são respostas diretas (streams, etc.)
            ProcessMessage(jsonMessage); // Chamamos o método antigo para compatibilidade
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar mensagem geral da API.");
        }
    }

    private void ProcessMessage(string jsonMessage)
    {
        try
        {
            using var doc = JsonDocument.Parse(jsonMessage);
            var root = doc.RootElement;
            if (!root.TryGetProperty("msg_type", out var msgTypeElement))
            {
                // Mensagem sem msg_type (ignorar)
                return;
            }
            var msgType = msgTypeElement.GetString();

            switch (msgType)
            {
                case "authorize":
                    if (root.TryGetProperty("error", out var error))
                    {
                        _logger.LogError($"Erro de autorização: {error.GetProperty("message").GetString()}");
                        _isAuthorizing = false;
                        _isAuthorized = false;
                        CheckAndNotifyConnectionStatusChange();
                        return;
                    }

                    var authResponse = root.GetProperty("authorize");
                    var loginid = authResponse.GetProperty("loginid").GetString() ?? string.Empty;
                    var accountType = authResponse.GetProperty("is_virtual").GetInt32() == 1 ? "Virtual" : "Real";
                    var balance = authResponse.GetProperty("balance").GetDouble();
                    AccountInfoUpdated?.Invoke(loginid, accountType, balance);
                    
                    // Inicia o timer de ping após autorização bem-sucedida
                    StartPingTimer();
                    
                    // Dispara o evento de conexão estabelecida após autorização bem-sucedida
                    _isAuthorizing = false;
                    _isAuthorized = true;
                    CheckAndNotifyConnectionStatusChange();
                    ConnectionEstablished?.Invoke();
                    
                    // Subscrever para atualizações de saldo e portfolio
                    _ = Task.Run(async () => {
                        await SubscribeToBalanceAsync();
                        await SubscribeToPortfolioAsync();
                    });
                    
                    // Iniciar sincronização frequente de saldo
                    StartBalanceSyncTimer();
                    break;
                
                case "balance":
                    var balanceResponse = root.GetProperty("balance");
                    var updatedBalance = balanceResponse.GetProperty("balance").GetDouble();
                    var loginIdBalance = balanceResponse.GetProperty("loginid").GetString() ?? string.Empty;
                    // Assumindo que o tipo de conta não muda, enviar string vazia para não sobrescrever
                    AccountInfoUpdated?.Invoke(loginIdBalance, string.Empty, updatedBalance);
                    break;
                    
                case "ping":
                    _pingStopwatch.Stop();
                    _lastPongReceived = DateTime.UtcNow; // Update last pong timestamp for connection health monitoring
                    PingUpdated?.Invoke(_pingStopwatch.ElapsedMilliseconds);
                    break;
                    
                case "portfolio":
                    ProcessPortfolioUpdate(root);
                    break;
                    
                case "proposal_open_contract":
                    ProcessOpenContractUpdate(root);
                    break;

                case "buy":
                    ProcessBuyResponse(root);
                    break;
                    
                case "tick":
                    ProcessTickUpdate(root);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar mensagem da API.");
        }
    }

    // Método genérico para enviar uma requisição e aguardar a resposta
    private async Task<JsonElement> SendRequestAsync<T>(T request)
    {
        var requestId = Interlocked.Increment(ref _nextRequestId);
        var tcs = new TaskCompletionSource<JsonElement>();
        _pendingRequests[requestId] = tcs;

        // Adiciona o req_id ao objeto de requisição
        var jsonRequest = JsonDocument.Parse(JsonSerializer.Serialize(request)).RootElement;
        var finalRequest = new Dictionary<string, object>();
        foreach (var property in jsonRequest.EnumerateObject())
        {
            finalRequest[property.Name] = property.Value;
        }
        finalRequest["req_id"] = requestId;

        if (_ws?.IsRunning != true)
            throw new InvalidOperationException("WebSocket não está conectado para enviar requisição.");

        _ws.Send(JsonSerializer.Serialize(finalRequest));

        // Timeout otimizado: reduzido para 2 segundos
        var completedTask = await Task.WhenAny(tcs.Task, Task.Delay(2000));
        if (completedTask != tcs.Task)
        {
            _pendingRequests.TryRemove(requestId, out _);
            throw new TimeoutException("A requisição para a API Deriv expirou.");
        }
        
        return await tcs.Task;
    }

    // Método otimizado para requisições críticas do Fast Martingale com retry automático
    private async Task<JsonElement> SendFastRequestAsync<T>(T request)
    {
        const int maxRetries = 3;
        const int timeoutMs = 5000; // Aumentado de 2 para 5 segundos
        
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                var requestId = Interlocked.Increment(ref _nextRequestId);
                var tcs = new TaskCompletionSource<JsonElement>();
                _pendingRequests[requestId] = tcs;

                // Adiciona o req_id ao objeto de requisição
                var jsonRequest = JsonDocument.Parse(JsonSerializer.Serialize(request)).RootElement;
                var finalRequest = new Dictionary<string, object>();
                foreach (var property in jsonRequest.EnumerateObject())
                {
                    finalRequest[property.Name] = property.Value;
                }
                finalRequest["req_id"] = requestId;

                if (_ws?.IsRunning != true)
                    throw new InvalidOperationException("WebSocket não está conectado para enviar requisição.");

                _ws.Send(JsonSerializer.Serialize(finalRequest));

                // Timeout aumentado para 5 segundos
                var completedTask = await Task.WhenAny(tcs.Task, Task.Delay(timeoutMs));
                if (completedTask != tcs.Task)
                {
                    _pendingRequests.TryRemove(requestId, out _);
                    
                    if (attempt == maxRetries)
                    {
                        _logger?.LogWarning($"Fast Martingale: Todas as {maxRetries} tentativas falharam por timeout");
                        throw new TimeoutException($"Requisição Fast Martingale expirou após {maxRetries} tentativas - latência alta detectada.");
                    }
                    
                    _logger?.LogWarning($"Fast Martingale: Tentativa {attempt}/{maxRetries} falhou por timeout, tentando novamente...");
                    await Task.Delay(500 * attempt); // Backoff progressivo
                    continue;
                }
                
                var result = await tcs.Task;
                if (attempt > 1)
                {
                    _logger?.LogInformation($"Fast Martingale: Sucesso na tentativa {attempt}/{maxRetries}");
                }
                return result;
            }
            catch (Exception ex) when (attempt < maxRetries && (ex is TimeoutException || ex is InvalidOperationException))
            {
                _logger?.LogWarning($"Fast Martingale: Tentativa {attempt}/{maxRetries} falhou: {ex.Message}");
                await Task.Delay(500 * attempt); // Backoff progressivo
            }
        }
        
        throw new TimeoutException($"Requisição Fast Martingale falhou após {maxRetries} tentativas.");
    }

    // Implementação dos novos métodos da interface
    public async Task<List<ActiveSymbol>> GetActiveSymbolsAsync()
    {
        var request = new { active_symbols = "full", product_type = "basic" };
        var response = await SendRequestAsync(request);
        var symbols = response.GetProperty("active_symbols").Deserialize<List<ActiveSymbol>>();
        return symbols ?? new List<ActiveSymbol>();
    }

    public async Task<ContractsForSymbol> GetContractsForSymbolAsync(string symbol)
    {
        var request = new { contracts_for = symbol };
        var response = await SendRequestAsync(request);
        
        // A propriedade raiz é "contracts_for"
        var contractsData = response.GetProperty("contracts_for").Deserialize<ContractsForSymbol>();
        return contractsData ?? new ContractsForSymbol();
    }

    public async Task<ProposalResponse> GetProposalAsync(ProposalRequest request)
    {
        var proposalRequest = new Dictionary<string, object>
        {
            ["proposal"] = 1,
            ["contract_type"] = request.ContractType,
            ["symbol"] = request.Symbol,
            ["duration"] = request.Duration,
            ["duration_unit"] = request.DurationUnit,
            ["currency"] = request.Currency,
            ["basis"] = request.Basis,
            ["amount"] = request.Stake
        };

        // Adicionar campos opcionais apenas se não forem nulos
        if (!string.IsNullOrEmpty(request.Barrier))
            proposalRequest["barrier"] = request.Barrier;
            
        if (!string.IsNullOrEmpty(request.Barrier2))
            proposalRequest["barrier2"] = request.Barrier2;
            
        if (request.LastDigitPrediction.HasValue)
            proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

        var response = await SendFastRequestAsync(proposalRequest);
        var proposalResponse = response.Deserialize<ProposalResponse>();
        return proposalResponse ?? new ProposalResponse();
    }

    public async Task<ProposalResponse> GetFastProposalAsync(ProposalRequest request)
    {
        var proposalRequest = new Dictionary<string, object>
        {
            ["proposal"] = 1,
            ["contract_type"] = request.ContractType,
            ["symbol"] = request.Symbol,
            ["duration"] = request.Duration,
            ["duration_unit"] = request.DurationUnit,
            ["currency"] = request.Currency,
            ["basis"] = request.Basis,
            ["amount"] = request.Stake
        };

        // Adicionar campos opcionais apenas se não forem nulos
        if (!string.IsNullOrEmpty(request.Barrier))
            proposalRequest["barrier"] = request.Barrier;
            
        if (!string.IsNullOrEmpty(request.Barrier2))
            proposalRequest["barrier2"] = request.Barrier2;
            
        if (request.LastDigitPrediction.HasValue)
            proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

        // Use the fastest possible request method with minimal timeout
        var response = await SendFastRequestAsync(proposalRequest);
        var proposalResponse = response.Deserialize<ProposalResponse>();
        return proposalResponse ?? new ProposalResponse();
    }

    public async Task<BuyResponse> BuyContractAsync(string proposalId, decimal price)
    {
        var startTime = DateTimeOffset.Now;

        var request = new
        {
            buy = proposalId,
            price = price,
            subscribe = 1  // Subscribe to contract updates
        };

        var response = await SendFastRequestAsync(request);
        var buyResponse = response.Deserialize<BuyResponse>();

        var endTime = DateTimeOffset.Now;
        var duration = (endTime - startTime).TotalMilliseconds;

        // Log contract purchase for debugging
        if (buyResponse?.Buy != null)
            _logger.LogInformation($"[ULTRA FAST BUY] Contract purchased in {duration:F1}ms: {buyResponse.Buy.ContractId}");

        // CORREÇÃO CRÍTICA: Processar resposta para disparar evento ContractPurchased
        if (buyResponse?.Buy != null)
        {
            _logger.LogInformation($"[BUY CONTRACT] Processing buy response for contract {buyResponse.Buy.ContractId} - will trigger ContractPurchased event");
            ProcessBuyResponse(response);
        }
        else
        {
            _logger.LogWarning($"[BUY CONTRACT] Buy failed - no contract created");
        }

        return buyResponse ?? new BuyResponse();
    }

    /// <summary>
    /// Ultra-fast dual contract purchase - executes both buys simultaneously for maximum speed
    /// </summary>
    public async Task<(BuyResponse buy1, BuyResponse buy2)> BuyDualContractsUltraFastAsync(
        string proposalId1, decimal price1, string proposalId2, decimal price2)
    {
        var startTime = DateTimeOffset.Now;
        _logger.LogInformation($"[ULTRA FAST DUAL] Starting simultaneous dual buy at {startTime:HH:mm:ss.fff}");

        // Create both requests simultaneously
        var request1 = new { buy = proposalId1, price = price1, subscribe = 1 };
        var request2 = new { buy = proposalId2, price = price2, subscribe = 1 };

        // Execute both buys simultaneously for maximum speed
        var task1 = SendFastRequestAsync(request1);
        var task2 = SendFastRequestAsync(request2);

        var responses = await Task.WhenAll(task1, task2);

        var buy1 = responses[0].Deserialize<BuyResponse>() ?? new BuyResponse();
        var buy2 = responses[1].Deserialize<BuyResponse>() ?? new BuyResponse();

        var endTime = DateTimeOffset.Now;
        var duration = (endTime - startTime).TotalMilliseconds;

        _logger.LogInformation($"[ULTRA FAST DUAL] Dual buy completed in {duration:F1}ms");
        if (buy1.Buy != null) _logger.LogInformation($"[ULTRA FAST DUAL] Contract 1: {buy1.Buy.ContractId}");
        if (buy2.Buy != null) _logger.LogInformation($"[ULTRA FAST DUAL] Contract 2: {buy2.Buy.ContractId}");

        // CORREÇÃO CRÍTICA: Processar respostas para disparar eventos ContractPurchased
        if (buy1.Buy != null)
        {
            _logger.LogInformation($"[ULTRA FAST DUAL] Processing buy1 response for contract {buy1.Buy.ContractId} - will trigger ContractPurchased event");
            ProcessBuyResponse(responses[0]);
        }
        else
        {
            _logger.LogWarning($"[ULTRA FAST DUAL] Buy1 failed - no contract created");
        }

        if (buy2.Buy != null)
        {
            _logger.LogInformation($"[ULTRA FAST DUAL] Processing buy2 response for contract {buy2.Buy.ContractId} - will trigger ContractPurchased event");
            ProcessBuyResponse(responses[1]);
        }
        else
        {
            _logger.LogWarning($"[ULTRA FAST DUAL] Buy2 failed - no contract created");
        }

        return (buy1, buy2);
    }

    // Ultra-fast same-time buy method for Fast Martingale - fire and forget approach
    public void BuyContractImmediateAsync(string proposalId, decimal price, Action<BuyResponse>? onComplete = null)
    {
        var immediateStartTime = DateTimeOffset.Now;
        
        // Create buy request
        var request = new
        {
            buy = proposalId,
            price = price,
            subscribe = 1
        };

        // Send immediately without waiting for response - fire and forget for same-time execution
        var jsonRequest = JsonSerializer.Serialize(request);
        
        try
        {
            // Send the WebSocket message immediately
            _ws?.Send(jsonRequest);
            
            var sendTime = DateTimeOffset.Now;
            var sendDelay = (sendTime - immediateStartTime).TotalMilliseconds;
            _logger.LogInformation($"[TIMING] SAME-TIME BUY: Mensagem de compra enviada em {sendDelay}ms às {sendTime:HH:mm:ss.fff}");
            
            // Handle response asynchronously without blocking
            if (onComplete != null)
            {
                // Set up a temporary response handler (simplified for immediate execution)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Wait briefly for response (non-blocking)
                        await Task.Delay(100); // Give time for server response
                        
                        // For immediate execution, we don't wait for the actual response
                        // The success will be confirmed by contract updates
                        var mockResponse = new BuyResponse(); // Simplified response
                        onComplete(mockResponse);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Erro no callback de compra imediata");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar compra imediata");
        }
    }

    public Task SubscribeToPortfolioAsync()
    {
        var request = new { portfolio = 1 };
        _ws?.Send(JsonSerializer.Serialize(request));
        // Reduced logging for performance
        return Task.CompletedTask;
    }

    private void ProcessPortfolioUpdate(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("portfolio", out var portfolio) && 
                portfolio.TryGetProperty("contracts", out var contracts))
            {
                foreach (var contract in contracts.EnumerateArray())
                {
                    var contractId = contract.TryGetProperty("contract_id", out var id) ? 
                        (id.ValueKind == JsonValueKind.String ? id.GetString() : id.GetInt64().ToString()) : "unknown";
                    var isSold = contract.TryGetProperty("is_sold", out var sold) ? sold.GetInt32() : 0;
                    
                    if (isSold == 1 && !string.IsNullOrEmpty(contractId))
                    {
                        ProcessFinishedContract(contractId, contract, "PORTFOLIO");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar atualização do portfolio.");
        }
    }
    
    private void ProcessOpenContractUpdate(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("proposal_open_contract", out var contract))
            {
                if (contract.TryGetProperty("contract_id", out var contractIdElement))
                {
                    string contractId;
                    
                    // Handle both string and number types for contract_id
                    if (contractIdElement.ValueKind == JsonValueKind.String)
                    {
                        contractId = contractIdElement.GetString() ?? string.Empty;
                    }
                    else if (contractIdElement.ValueKind == JsonValueKind.Number)
                    {
                        contractId = contractIdElement.GetInt64().ToString();
                    }
                    else
                    {
                        return; // Skip if neither string nor number
                    }
                    
                    // Check if contract is near expiry (for Fast Martingale pre-calculation)
                    // Propagate entry tick/time as soon as they're available in any proposal_open_contract payload
                    if (contract.TryGetProperty("entry_tick", out var earlyEntryTickElem) &&
                        contract.TryGetProperty("entry_tick_time", out var earlyEntryTickTimeElem))
                    {
                        try
                        {
                            var earlyEntryTick = earlyEntryTickElem.GetDecimal();
                            var earlyEntryTickTime = earlyEntryTickTimeElem.GetInt64();
                            // Diagnostic log: raw epoch and converted UTC time
                            try
                            {
                                var entryDt = DateTimeOffset.FromUnixTimeSeconds(earlyEntryTickTime).UtcDateTime;
                                _logger.LogInformation($"[DEBUG] entry_tick for {contractId}: epoch={earlyEntryTickTime} -> {entryDt:HH:mm:ss.fff}, price={earlyEntryTick}");
                            }
                            catch { /* ignore conversion logging errors */ }

                            ContractEntryTickReceived?.Invoke(contractId, earlyEntryTick, earlyEntryTickTime);
                        }
                        catch { /* ignore parse issues */ }
                    }

                    if (contract.TryGetProperty("date_expiry", out var dateExpiryElement) && 
                        contract.TryGetProperty("is_sold", out var isSoldElement))
                    {
                        var isSold = isSoldElement.GetInt32();
                        
                        if (isSold == 0) // Contract is still active
                        {
                            var dateExpiry = dateExpiryElement.GetInt64();
                            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                            var timeToExpiry = dateExpiry - currentTime;
                            
                            // If contract expires in 5 seconds or less, trigger near expiry event (gives more prep time)
                            if (timeToExpiry <= 5 && timeToExpiry > 0)
                            {
                                _logger.LogInformation($"Contract {contractId} near expiry: {timeToExpiry}s");
                                ContractNearExpiry?.Invoke(contractId);
                            }
                            
                            // ULTRA-IMMEDIATE DETECTION: Check if contract has just expired (within 2 seconds for maximum coverage)
                            if (timeToExpiry <= 0 && timeToExpiry >= -2)
                            {
                                _logger.LogInformation($"ULTRA-IMMEDIATE: Contrato {contractId} expirado há {Math.Abs(timeToExpiry):F1}s - processando imediatamente");
                                if (ProcessFinishedContract(contractId, contract, "ULTRA-IMMEDIATE"))
                                {
                                    return; // Exit early to avoid double processing
                                }
                            }
                        }
                    }
                    
                    // Fallback: Check if contract is sold/finished (existing logic)
                    if (contract.TryGetProperty("is_sold", out var isSoldElement2))
                    {
                        var isSold = isSoldElement2.GetInt32();
                        
                        if (isSold == 1) // Contract is sold/finished
                        {
                            _logger.LogInformation($"[TIMING] FALLBACK - Contrato {contractId} detectado como finalizado");
                            ProcessFinishedContract(contractId, contract, "FALLBACK");
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar atualização de contrato aberto.");
        }
    }

    // Start a proposal subscription (subscribe=1) and cache latest proposal for ultra-fast subsequent buy
    public async Task<string> StartProposalSubscriptionAsync(ProposalRequest request, string key)
    {
        try
        {
            var proposalRequest = new Dictionary<string, object>
            {
                ["proposal"] = 1,
                ["contract_type"] = request.ContractType,
                ["symbol"] = request.Symbol,
                ["duration"] = request.Duration,
                ["duration_unit"] = request.DurationUnit,
                ["currency"] = request.Currency,
                ["basis"] = request.Basis,
                ["amount"] = request.Stake,
                ["subscribe"] = 1
            };

            if (!string.IsNullOrEmpty(request.Barrier)) proposalRequest["barrier"] = request.Barrier;
            if (!string.IsNullOrEmpty(request.Barrier2)) proposalRequest["barrier2"] = request.Barrier2;
            if (request.LastDigitPrediction.HasValue) proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

            var response = await SendFastRequestAsync(proposalRequest);
            // First response contains subscription id
            if (response.TryGetProperty("subscription", out var subElem) && subElem.TryGetProperty("id", out var idElem))
            {
                var subId = idElem.GetString() ?? string.Empty;
                if (!string.IsNullOrEmpty(subId))
                {
                    lock (_proposalStreamCache)
                    {
                        _proposalStreamKeyBySubId[subId] = key;
                        _proposalStreamSubIdByKey[key] = subId;
                    }

                    // Save the first snapshot if present
                    var snap = response.Deserialize<ProposalResponse>();
                    if (snap?.Proposal != null)
                    {
                        _proposalStreamCache[key] = snap;
                    }
                }
            }
            return key;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PROPOSAL STREAM] Failed to start proposal subscription");
            return key;
        }
    }

    public void StopProposalSubscription(string key)
    {
        try
        {
            string? subId = null;
            lock (_proposalStreamCache)
            {
                if (_proposalStreamSubIdByKey.TryGetValue(key, out var id))
                {
                    subId = id;
                    _proposalStreamSubIdByKey.Remove(key);
                    _proposalStreamKeyBySubId.Remove(id);
                    _proposalStreamCache.Remove(key);
                }
            }
            if (!string.IsNullOrEmpty(subId))
            {
                var forget = new { forget = subId };
                _ws?.Send(JsonSerializer.Serialize(forget));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "[PROPOSAL STREAM] Failed to stop proposal subscription");
        }
    }

    public ProposalResponse? GetLatestProposalFromStream(string key)
    {
        lock (_proposalStreamCache)
        {
            return _proposalStreamCache.TryGetValue(key, out var p) ? p : null;
        }
    }

    /// <summary>
    /// Método unificado e thread-safe para processar contratos finalizados
    /// Evita duplicação e garante cálculo consistente de profit
    /// </summary>
    private bool ProcessFinishedContract(string contractId, JsonElement contract, string source)
    {
        // Thread-safe check and add - evita processamento duplicado
        lock (_contractProcessingLock)
        {
            if (_processedContracts.Contains(contractId))
            {
                _logger.LogDebug($"[{source}] Contrato {contractId} já foi processado - ignorando");
                return false; // Já processado
            }
            
            // Marcar como processado imediatamente para evitar race conditions
            _processedContracts.Add(contractId);
        }

        try
        {
            if (!contract.TryGetProperty("profit", out var profitElement))
            {
                _logger.LogWarning($"[{source}] Contrato {contractId} sem informação de profit");
                return false;
            }

            var apiProfit = profitElement.GetDecimal();
            bool isWin = apiProfit > 0;
            decimal correctedProfit = apiProfit;

            // LÓGICA UNIFICADA DE CORREÇÃO DE PROFIT
            if (contract.TryGetProperty("buy_price", out var buyPriceElement) &&
                contract.TryGetProperty("payout", out var payoutElement))
            {
                var stake = buyPriceElement.GetDecimal();
                var payout = payoutElement.GetDecimal();

                // Cálculo correto: WIN = payout - stake, LOSS = -stake
                correctedProfit = isWin ? (payout - stake) : -stake;

                _logger.LogInformation($"[{source}] Contrato {contractId}: API profit={apiProfit:F2}, Corrected={correctedProfit:F2}, Stake={stake:F2}, Payout={payout:F2}");

                if (Math.Abs(apiProfit - correctedProfit) > 0.01m)
                {
                    _logger.LogWarning($"[{source}] PROFIT MISMATCH - Contrato {contractId}: API={apiProfit:F2} vs Calculado={correctedProfit:F2}");
                }
            }

            // Extrair preço e tempo de saída
            decimal exitPrice = 0;
            DateTime exitTime = DateTime.UtcNow;

            // Tentar múltiplos campos para preço de saída
            if (contract.TryGetProperty("exit_tick", out var exitTickElement))
            {
                exitPrice = exitTickElement.GetDecimal();
            }
            else if (contract.TryGetProperty("sell_spot", out var sellSpotElement))
            {
                exitPrice = sellSpotElement.GetDecimal();
            }
            else if (contract.TryGetProperty("exit_tick_display_value", out var exitDisplayElement))
            {
                if (decimal.TryParse(exitDisplayElement.GetString()?.Replace(",", "."), NumberStyles.Float, CultureInfo.InvariantCulture, out var parsedPrice))
                {
                    exitPrice = parsedPrice;
                }
            }
            else if (contract.TryGetProperty("current_spot", out var currentSpotElement))
            {
                exitPrice = currentSpotElement.GetDecimal();
            }

            // Tentar obter tempo de saída
            if (contract.TryGetProperty("sell_time", out var sellTimeElement))
            {
                var sellTimeUnix = sellTimeElement.GetInt64();
                exitTime = DateTimeOffset.FromUnixTimeSeconds(sellTimeUnix).UtcDateTime;
            }
            else if (contract.TryGetProperty("date_expiry", out var expiryElement))
            {
                var expiryUnix = expiryElement.GetInt64();
                exitTime = DateTimeOffset.FromUnixTimeSeconds(expiryUnix).UtcDateTime;
            }

            var processTime = DateTimeOffset.Now;
            _logger.LogInformation($"[{source}] Contrato {contractId} processado às {processTime:HH:mm:ss.fff} - Profit: {correctedProfit:F2}, Win: {isWin}, Exit: {exitPrice:F4}");

            // Disparar eventos de forma thread-safe
            ContractResult?.Invoke(isWin);
            ContractFinished?.Invoke(contractId, correctedProfit, exitPrice, exitTime);

            // Propagar entry_tick se disponível
            if (contract.TryGetProperty("entry_tick", out var entryTickElement) &&
                contract.TryGetProperty("entry_tick_time", out var entryTickTimeElement))
            {
                try
                {
                    var entryTick = entryTickElement.GetDecimal();
                    var entryTickTime = entryTickTimeElement.GetInt64();
                    ContractEntryTickReceived?.Invoke(contractId, entryTick, entryTickTime);
                }
                catch { /* ignore parse errors */ }
            }

            return true; // Processado com sucesso
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[{source}] Erro ao processar contrato finalizado {contractId}");
            return false;
        }
    }
    
    public void GetFastProposalAndBuyAsync(ProposalRequest request, Action<ProposalResponse, BuyResponse> onComplete)
    {
        var operationStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] ZERO-DELAY OPERATION: GetFastProposalAndBuyAsync iniciado às {operationStartTime:HH:mm:ss.fff}");
        
        // Execute both operations in parallel for maximum speed
        _ = Task.Run(async () =>
        {
            try
            {
                var parallelStartTime = DateTimeOffset.Now;
                
                // Step 1: Get proposal with ultra-low latency
                var proposalTask = GetFastProposalAsync(request);
                var proposalResponse = await proposalTask;
                
                var proposalEndTime = DateTimeOffset.Now;
                var proposalDelay = (proposalEndTime - parallelStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ZERO-DELAY: Proposta recebida em {proposalDelay}ms às {proposalEndTime:HH:mm:ss.fff}");
                
                BuyResponse buyResponse = new BuyResponse();
                
                // Step 2: Execute buy immediately if proposal is valid
                if (proposalResponse?.Error == null && proposalResponse?.Proposal != null)
                {
                    var buyStartTime = DateTimeOffset.Now;
                    
                    // Execute buy with immediate fire-and-forget pattern
                    var buyTaskCompletion = new TaskCompletionSource<BuyResponse>();
                    
                    if (string.IsNullOrEmpty(proposalResponse.Proposal.Id))
                    {
                        buyResponse = new BuyResponse { Error = new ApiError { Message = "Invalid proposal id" } };
                    }
                    else
                    {
                    BuyContractImmediateAsync(proposalResponse.Proposal.Id, proposalResponse.Proposal.AskPrice, (response) =>
                    {
                        buyResponse = response;
                        buyTaskCompletion.SetResult(response);
                        
                        var buyEndTime = DateTimeOffset.Now;
                        var buyDelay = (buyEndTime - buyStartTime).TotalMilliseconds;
                        _logger.LogInformation($"[TIMING] ZERO-DELAY: Compra executada em {buyDelay}ms às {buyEndTime:HH:mm:ss.fff}");
                    });
                    }
                    
                    // Wait for buy to complete with timeout for safety
                    try
                    {
                        buyResponse = await Task.WhenAny(buyTaskCompletion.Task, Task.Delay(1000)) == buyTaskCompletion.Task 
                            ? await buyTaskCompletion.Task 
                            : new BuyResponse { Error = new ApiError { Message = "Buy timeout" } };
                    }
                    catch (Exception buyEx)
                    {
                        _logger.LogError(buyEx, "[TIMING] ZERO-DELAY: Erro na execução da compra");
                        buyResponse = new BuyResponse { Error = new ApiError { Message = buyEx.Message } };
                    }
                }
                else
                {
                    _logger.LogError($"[TIMING] ZERO-DELAY: Proposta inválida - {proposalResponse?.Error?.Message}");
                    buyResponse = new BuyResponse { Error = new ApiError { Message = "Invalid proposal" } };
                }
                
                var operationEndTime = DateTimeOffset.Now;
                var totalOperationTime = (operationEndTime - operationStartTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] ZERO-DELAY OPERATION: Total time {totalOperationTime}ms às {operationEndTime:HH:mm:ss.fff}");
                
                // Execute callback with both responses
                onComplete?.Invoke(proposalResponse ?? new ProposalResponse(), buyResponse ?? new BuyResponse());
            }
            catch (Exception ex)
            {
                var errorTime = DateTimeOffset.Now;
                var errorDelay = (errorTime - operationStartTime).TotalMilliseconds;
                _logger.LogError(ex, $"[TIMING] ZERO-DELAY OPERATION: Erro crítico após {errorDelay}ms");
                
                // Return error responses
                var errorProposal = new ProposalResponse { Error = new ProposalError { Message = ex.Message } };
                var errorBuy = new BuyResponse { Error = new ApiError { Message = ex.Message } };
                onComplete?.Invoke(errorProposal, errorBuy);
            }
        });
    }

    // REVOLUTIONARY INSTANT BUY: Uses pre-calculated proposal pool for zero-delay execution
    public void BuyInstantMarketAsync(ProposalRequest request, Action<BuyResponse>? onComplete = null)
    {
        var instantStartTime = DateTimeOffset.Now;
        _logger.LogInformation($"[TIMING] INSTANT POOL BUY: Execução iniciada às {instantStartTime:HH:mm:ss.fff}");
        
        _ = Task.Run(async () =>
        {
            try
            {
                // INSTANT PROPOSAL REUSE: Try to get a very recent proposal first
                var hotProposal = await GetHotProposalAsync(request);
                
                if (hotProposal?.Error == null && hotProposal?.Proposal != null)
                {
                    var proposalTime = DateTimeOffset.Now;
                    var proposalDelay = (proposalTime - instantStartTime).TotalMilliseconds;
                    _logger.LogInformation($"[TIMING] INSTANT POOL: Proposta obtida em {proposalDelay}ms às {proposalTime:HH:mm:ss.fff}");

                    // Enviar BUY e aguardar resposta (evento ContractPurchased virá do stream 'buy')
                    var proposalId = hotProposal.Proposal.Id;
                    if (string.IsNullOrEmpty(proposalId))
                    {
                        onComplete?.Invoke(new BuyResponse { Error = new ApiError { Message = "Invalid proposal id (null/empty)" } });
                        return;
                    }
                    var buyResponse = await BuyContractAsync(proposalId, hotProposal.Proposal.AskPrice);
                    onComplete?.Invoke(buyResponse);
                }
                else
                {
                    // FALLBACK: Create new proposal if pool is empty
                    _logger.LogInformation($"[TIMING] INSTANT POOL: Sem proposta disponível, criando nova");
                    
                    var fallbackProposal = await GetFastProposalAsync(request);
                    
                    if (fallbackProposal?.Error == null && fallbackProposal?.Proposal != null)
                    {
                        var proposalId = fallbackProposal.Proposal.Id;
                        if (string.IsNullOrEmpty(proposalId))
                        {
                            onComplete?.Invoke(new BuyResponse { Error = new ApiError { Message = "Invalid fallback proposal id (null/empty)" } });
                            return;
                        }
                        var buyResponse = await BuyContractAsync(proposalId, fallbackProposal.Proposal.AskPrice);
                        onComplete?.Invoke(buyResponse);
                    }
                    else
                    {
                        _logger.LogError($"[TIMING] INSTANT POOL: Erro na proposta fallback - {fallbackProposal?.Error?.Message}");
                        
                        if (onComplete != null)
                        {
                            var errorResponse = new BuyResponse { Error = new ApiError { Message = "No proposals available" } };
                            onComplete(errorResponse);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var errorTime = DateTimeOffset.Now;
                var errorDelay = (errorTime - instantStartTime).TotalMilliseconds;
                _logger.LogError(ex, $"[TIMING] INSTANT POOL BUY: Erro após {errorDelay}ms");
                
                if (onComplete != null)
                {
                    var errorResponse = new BuyResponse { Error = new ApiError { Message = ex.Message } };
                    onComplete(errorResponse);
                }
            }
        });
    }
    
    // Ultra-fast proposal method optimized for instant reuse
    private async Task<ProposalResponse> GetHotProposalAsync(ProposalRequest request)
    {
        try
        {
            // Create proposal request with minimal overhead
            var proposalRequest = new Dictionary<string, object>
            {
                ["proposal"] = 1,
                ["contract_type"] = request.ContractType,
                ["symbol"] = request.Symbol,
                ["duration"] = request.Duration,
                ["duration_unit"] = request.DurationUnit,
                ["currency"] = request.Currency,
                ["basis"] = request.Basis,
                ["amount"] = request.Stake
            };

            // Add optional parameters
            if (!string.IsNullOrEmpty(request.Barrier))
                proposalRequest["barrier"] = request.Barrier;
                
            if (!string.IsNullOrEmpty(request.Barrier2))
                proposalRequest["barrier2"] = request.Barrier2;
                
            if (request.LastDigitPrediction.HasValue)
                proposalRequest["last_digit_prediction"] = request.LastDigitPrediction.Value;

            // Use ultra-fast request with reduced timeout for hot proposals
            var response = await SendFastRequestAsync(proposalRequest);
            var proposalResponse = response.Deserialize<ProposalResponse>();
            return proposalResponse ?? new ProposalResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter hot proposal");
            return new ProposalResponse { Error = new ProposalError { Message = ex.Message } };
        }
    }
    
    // ULTRA-FAST DIRECT SEND: Bypasses all intermediate processing for true sub-100ms execution
    public void SendDirectBuyCommand(string buyJson)
    {
        try
        {
            if (_ws?.IsRunning == true)
            {
                var sendTime = DateTimeOffset.Now;
                _ws.Send(buyJson);
                var afterSend = DateTimeOffset.Now;
                var sendDelay = (afterSend - sendTime).TotalMilliseconds;
                _logger.LogInformation($"[TIMING] DIRECT SEND: WebSocket message sent in {sendDelay}ms at {afterSend:HH:mm:ss.fff}");
            }
            else
            {
                _logger.LogError("[CRITICAL] DIRECT SEND: WebSocket not running - cannot send direct command");
                throw new InvalidOperationException("WebSocket connection not available");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[CRITICAL] DIRECT SEND: Failed to send direct buy command");
            throw;
        }
    }
    
    // TICKS SUBSCRIPTION: Subscribe to real-time price ticks for selected symbol
    public Task SubscribeToTicksAsync(string symbol)
    {
        try
        {
            // Unsubscribe from current ticks if any
            UnsubscribeFromTicks();
            
            var request = new { ticks = symbol, subscribe = 1 };
            var jsonRequest = JsonSerializer.Serialize(request);
            _ws?.Send(jsonRequest);
            
            _currentTicksSubscription = symbol;
            _logger.LogInformation($"[TICKS] Subscribed to ticks for symbol: {symbol}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[TICKS] Failed to subscribe to ticks for symbol: {symbol}");
        }
        return Task.CompletedTask;
    }
    
    // TICKS UNSUBSCRIPTION: Stop receiving ticks for current symbol
    public void UnsubscribeFromTicks()
    {
        try
        {
            if (!string.IsNullOrEmpty(_currentTicksSubscription))
            {
                var request = new { forget_all = "ticks" };
                var jsonRequest = JsonSerializer.Serialize(request);
                _ws?.Send(jsonRequest);
                
                _logger.LogInformation($"[TICKS] Unsubscribed from ticks for symbol: {_currentTicksSubscription}");
                _currentTicksSubscription = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[TICKS] Failed to unsubscribe from ticks");
        }
    }
    
    // TICKS PROCESSING: Handle incoming tick data
    private void ProcessTickUpdate(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("tick", out var tick))
            {
                    if (tick.TryGetProperty("quote", out var quoteElement) && 
                    tick.TryGetProperty("epoch", out var epochElement))
                    {
                    var price = quoteElement.GetDecimal();
                    var epoch = epochElement.GetInt64();
                    // Always use UtcDateTime to avoid mixed kinds and timezone conversion bugs
                    var timestamp = DateTimeOffset.FromUnixTimeSeconds(epoch).UtcDateTime;
                    
                    // Trigger tick received event
                    TickReceived?.Invoke(price, timestamp);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[TICKS] Error processing tick update");
        }
    }

    private void ProcessBuyResponse(JsonElement root)
    {
        try
        {
            if (root.TryGetProperty("buy", out var buyElement))
            {
                var contractId = buyElement.GetProperty("contract_id").GetInt64().ToString();
                var longcode = buyElement.TryGetProperty("longcode", out var lc) ? lc.GetString() ?? string.Empty : string.Empty;
                var buyPrice = buyElement.GetProperty("buy_price").GetDecimal();
                var payout = buyElement.GetProperty("payout").GetDecimal();
                var purchaseTime = buyElement.GetProperty("purchase_time").GetInt64();

                // Extract contract type and duration from longcode
                var contractType = ExtractContractTypeFromLongcode(longcode ?? string.Empty);
                var duration = ExtractDurationFromLongcode(longcode ?? string.Empty);

                var purchaseDateTime = DateTimeOffset.FromUnixTimeSeconds(purchaseTime).UtcDateTime;

                _logger.LogInformation($"Buy response processed: Contract {contractId}, Type: {contractType}, Stake: {buyPrice}");

                // Trigger contract purchased event
                ContractPurchased?.Invoke(contractId, contractType, duration, buyPrice, payout, purchaseDateTime);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar resposta de compra.");
        }
    }

    private string ExtractContractTypeFromLongcode(string longcode)
    {
        if (string.IsNullOrEmpty(longcode)) return "Unknown";

        // Extract contract type from longcode (simplified extraction)
        if (longcode.Contains("Rise")) return "Rise";
        if (longcode.Contains("Fall")) return "Fall";
        if (longcode.Contains("Call")) return "Call";
        if (longcode.Contains("Put")) return "Put";
        if (longcode.Contains("Higher")) return "Higher";
        if (longcode.Contains("Lower")) return "Lower";
        if (longcode.Contains("Touch")) return "Touch";
        if (longcode.Contains("No Touch")) return "No Touch";
        if (longcode.Contains("Ends Between")) return "Ends Between";
        if (longcode.Contains("Ends Outside")) return "Ends Outside";
        if (longcode.Contains("Stays Between")) return "Stays Between";
        if (longcode.Contains("Goes Outside")) return "Goes Outside";
        if (longcode.Contains("Matches")) return "Matches";
        if (longcode.Contains("Differs")) return "Differs";

        return "Unknown";
    }

    private string ExtractDurationFromLongcode(string longcode)
    {
        if (string.IsNullOrEmpty(longcode)) return "---";

        try
        {
            // Match patterns like '1 tick', '5 ticks', '30 second(s)', '1 min', etc.
            var match = System.Text.RegularExpressions.Regex.Match(longcode, "(\\d+)\\s*(tick|ticks|second|seconds|sec|minute|minutes|min|hour|hours|hr)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (match.Success)
            {
                var number = match.Groups[1].Value;
                var unit = match.Groups[2].Value.ToLowerInvariant();
                unit = unit switch
                {
                    "tick" or "ticks" => (number == "1") ? "tick" : "ticks",
                    "second" or "seconds" or "sec" => (number == "1") ? "sec" : "secs",
                    "minute" or "minutes" or "min" => (number == "1") ? "min" : "mins",
                    "hour" or "hours" or "hr" => (number == "1") ? "hr" : "hrs",
                    _ => unit
                };
                return $"{number} {unit}";
            }
        }
        catch { /* fall through to default */ }

        return "---";
    }
}
