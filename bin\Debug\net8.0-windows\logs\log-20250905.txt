2025-09-05 09:58:13.077 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-05 09:58:13.194 -03:00 [INF] Hosting environment: Production
2025-09-05 09:58:13.195 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.5\bin\Debug\net8.0-windows
2025-09-05 09:58:14.716 -03:00 [INF] Conectando à API Deriv...
2025-09-05 09:58:15.528 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-05 09:58:16.635 -03:00 [INF] [BALANCE] Initial balance set to: 56936,68
2025-09-05 09:58:16.638 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-05 09:58:16.647 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-05 09:58:17.206 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-05 09:58:17.209 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-05 09:58:17.210 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-05 09:58:17.210 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-05 09:58:17.210 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-05 09:58:17.210 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-05 09:58:17.210 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-05 09:58:17.210 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-05 09:58:17.210 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-05 09:58:17.211 -03:00 [INF] [DEBUG] ConnectionEstablished evento disparado
2025-09-05 09:58:17.211 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-05 09:58:17.216 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-05 09:58:17.434 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-05 09:58:17.435 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-05 09:58:17.435 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-05 09:58:17.435 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-05 09:58:17.436 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-05 09:58:17.436 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-05 09:58:17.436 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-05 09:58:17.436 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-05 09:58:17.436 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-05 09:58:17.438 -03:00 [INF] [DEBUG] Seleções restauradas: Market=, SubMarket=, Symbol=, ContractType=
2025-09-05 09:59:53.499 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-05 09:59:53.506 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1,00
2025-09-05 09:59:53.545 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-05 09:59:55.666 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-05 09:59:55.666 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=1,00
2025-09-05 09:59:57.837 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-05 09:59:57.837 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0'
2025-09-05 09:59:58.065 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-05 09:59:58.065 -03:00 [INF] [DEBUG] Saindo: StakeAmount inválido: '0.'
2025-09-05 09:59:58.214 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-05 09:59:58.214 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0,3
2025-09-05 09:59:58.303 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-05 09:59:58.303 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=CALLE, SelectedActiveSymbol=R_10, IsCalculating=True
2025-09-05 09:59:59.831 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-05 09:59:59.831 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0,35
2025-09-05 10:00:13.433 -03:00 [INF] [BUY] ExecuteBuyCommand called - IsDualEnabled: True
2025-09-05 10:00:13.434 -03:00 [INF] [BUY] Dual mode detected, calling ExecuteDualEntryCommand
2025-09-05 10:00:13.440 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 1/5, Session 1/10000
2025-09-05 10:00:13.450 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:13.639 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=9df1ba75-9785-dc8a-0b83-a218a528fa5e, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:13.640 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:13.833 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=a4dec842-61bb-c1b0-ff8f-3226142365b9, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:13.833 -03:00 [INF] [DUAL] Payouts obtidos - Contrato 1: 0,66, Contrato 2: 0,66
2025-09-05 10:00:13.834 -03:00 [INF] [DUAL] PRIMEIRA ENTRADA - Stake menor: 0,35 (campo Stake), Stake maior: 0,50 (calculada para Take Profit: 0,03)
2025-09-05 10:00:13.834 -03:00 [INF] [DUAL] Cálculo: higherStake1=0,43, higherStake2=0,43, média=0,50
2025-09-05 10:00:13.836 -03:00 [INF] [DUAL] Primeira entrada - Random choice: Contrato principal MAIOR stake
2025-09-05 10:00:13.836 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0,50, Lower: 0,35
2025-09-05 10:00:13.836 -03:00 [INF] [DUAL] Stake MAIOR (0,50) vai para: Higher
2025-09-05 10:00:13.836 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,50
2025-09-05 10:00:14.011 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=2eab9df1-321e-f414-f56f-aaef7ad9cd30, AskPrice=0,50, Payout=0,96
2025-09-05 10:00:14.011 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:14.270 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=a4dec842-61bb-c1b0-ff8f-3226142365b9, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:14.270 -03:00 [INF] [DUAL] Payouts - Contrato 1: 0,96, Contrato 2: 0,66
2025-09-05 10:00:14.270 -03:00 [INF] [DUAL] Executando compras - Proposal1: 2eab9df1-321e-f414-f56f-aaef7ad9cd30, Price1: 0,50
2025-09-05 10:00:14.270 -03:00 [INF] [DUAL] Executando compras - Proposal2: a4dec842-61bb-c1b0-ff8f-3226142365b9, Price2: 0,35
2025-09-05 10:00:14.528 -03:00 [INF] Contract 293294319968 near expiry: 2s
2025-09-05 10:00:14.565 -03:00 [INF] [DEBUG] Contrato comprado: 293294319968, subscrevendo para atualizações
2025-09-05 10:00:14.567 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294319968, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:14.567 -03:00 [INF] [DEBUG] Contrato comprado: 293294319988, subscrevendo para atualizações
2025-09-05 10:00:14.568 -03:00 [INF] Contract 293294319988 near expiry: 2s
2025-09-05 10:00:14.568 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-05 10:00:14.594 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294319988, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:16.379 -03:00 [INF] [DEBUG] entry_tick for 293294319968: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:16.382 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319968: 5900,863 at 13:00:16
2025-09-05 10:00:16.383 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294319968 expirado às 10:00:16.382 - Profit: -0,04, Win: False
2025-09-05 10:00:16.383 -03:00 [INF] [DEBUG] exit_time for 293294319968: date_expiry_epoch=1757077216 -> 13:00:16.000
2025-09-05 10:00:16.384 -03:00 [INF] [DUAL] Contract result received at 10:00:16.384 - WIN: False
2025-09-05 10:00:16.385 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-05 10:00:16.385 -03:00 [INF] [DUAL] Contracts completed: 1/2
2025-09-05 10:00:16.389 -03:00 [INF] Profit Table updated for contract 293294319968: Profit=-0,04, ExitPrice=0, ExitTime=13:00:16
2025-09-05 10:00:16.389 -03:00 [INF] [DUAL] Contract 1 finished: Stake=0,50, Profit=-0,04
2025-09-05 10:00:16.392 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319968: 5900,863 at 13:00:16
2025-09-05 10:00:16.393 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 9,5929ms
2025-09-05 10:00:16.393 -03:00 [INF] [DEBUG] entry_tick for 293294319988: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:16.393 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319988: 5900,863 at 13:00:16
2025-09-05 10:00:16.394 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294319988 expirado às 10:00:16.394 - Profit: -0,04, Win: False
2025-09-05 10:00:16.394 -03:00 [INF] [DEBUG] exit_time for 293294319988: date_expiry_epoch=1757077216 -> 13:00:16.000
2025-09-05 10:00:16.394 -03:00 [INF] [DUAL] Contract result received at 10:00:16.394 - WIN: False
2025-09-05 10:00:16.394 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-05 10:00:16.394 -03:00 [INF] [DUAL] Contracts completed: 2/2
2025-09-05 10:00:16.394 -03:00 [INF] [DUAL] Both contracts completed, processing level
2025-09-05 10:00:16.395 -03:00 [INF] [DUAL] Processing level 2 complete. Session profit: 0,00
2025-09-05 10:00:16.395 -03:00 [INF] [DUAL] Advancing to level 3. Executing next dual entry in 2 seconds...
2025-09-05 10:00:16.396 -03:00 [INF] Profit Table updated for contract 293294319988: Profit=-0,04, ExitPrice=0, ExitTime=13:00:16
2025-09-05 10:00:16.397 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319988: 5900,863 at 13:00:16
2025-09-05 10:00:16.397 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 3,1577ms
2025-09-05 10:00:16.397 -03:00 [INF] [DEBUG] entry_tick for 293294319968: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:16.401 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319968: 5900,863 at 13:00:16
2025-09-05 10:00:16.401 -03:00 [INF] [DEBUG] entry_tick for 293294319988: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:16.402 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319988: 5900,863 at 13:00:16
2025-09-05 10:00:18.400 -03:00 [INF] [DUAL] Auto-executing next level 3
2025-09-05 10:00:18.400 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 3/5, Session 1/10000
2025-09-05 10:00:18.400 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:18.430 -03:00 [INF] [DEBUG] entry_tick for 293294319968: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:18.430 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319968: 5900,863 at 13:00:16
2025-09-05 10:00:18.441 -03:00 [INF] [DEBUG] entry_tick for 293294319968: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:18.442 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319968: 5900,863 at 13:00:16
2025-09-05 10:00:18.456 -03:00 [INF] [DEBUG] entry_tick for 293294319988: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:18.456 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319988: 5900,863 at 13:00:16
2025-09-05 10:00:18.457 -03:00 [INF] [DEBUG] entry_tick for 293294319988: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:18.457 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319988: 5900,863 at 13:00:16
2025-09-05 10:00:18.590 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=9df1ba75-9785-dc8a-0b83-a218a528fa5e, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:18.590 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:18.772 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=c3fa5414-d1c4-78b1-14f2-029f296d4b58, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:18.772 -03:00 [INF] [DUAL] Payouts obtidos - Contrato 1: 0,66, Contrato 2: 0,66
2025-09-05 10:00:18.772 -03:00 [INF] [DUAL] ENTRADA SUBSEQUENTE - Nível 3
2025-09-05 10:00:18.772 -03:00 [INF] [DUAL] Prejuízo acumulado a recuperar: 0,00
2025-09-05 10:00:18.772 -03:00 [INF] [DUAL] SessionProfit atual: 0,00
2025-09-05 10:00:18.772 -03:00 [INF] [DUAL] Sem prejuízo acumulado, calculando para Take Profit 0,03: higherStake=0,43
2025-09-05 10:00:18.772 -03:00 [INF] [DUAL] Após recuperar prejuízo, SessionProfit seria 0,00, precisando de mais 0,03 para Take Profit
2025-09-05 10:00:18.772 -03:00 [INF] [DUAL] Reajustando stake maior para 0,43 para atingir Take Profit total
2025-09-05 10:00:18.772 -03:00 [INF] [DUAL] Stakes finais - Maior: 0,50, Menor: 0,35
2025-09-05 10:00:18.773 -03:00 [INF] [DUAL] Entrada subsequente - Stake MAIOR vai para: Contrato 2 (que perdeu na rodada anterior)
2025-09-05 10:00:18.773 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0,35, Lower: 0,50
2025-09-05 10:00:18.773 -03:00 [INF] [DUAL] Stake MAIOR (0,50) vai para: Lower
2025-09-05 10:00:18.773 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:18.963 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=9df1ba75-9785-dc8a-0b83-a218a528fa5e, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:18.963 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,50
2025-09-05 10:00:19.188 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=31249258-625c-4d1e-2ac7-395145a06dcf, AskPrice=0,50, Payout=0,96
2025-09-05 10:00:19.188 -03:00 [INF] [DUAL] Payouts - Contrato 1: 0,66, Contrato 2: 0,96
2025-09-05 10:00:19.188 -03:00 [INF] [DUAL] Executando compras - Proposal1: 9df1ba75-9785-dc8a-0b83-a218a528fa5e, Price1: 0,35
2025-09-05 10:00:19.188 -03:00 [INF] [DUAL] Executando compras - Proposal2: 31249258-625c-4d1e-2ac7-395145a06dcf, Price2: 0,50
2025-09-05 10:00:19.417 -03:00 [INF] [DEBUG] Contrato comprado: 293294326188, subscrevendo para atualizações
2025-09-05 10:00:19.428 -03:00 [INF] [DEBUG] Contrato comprado: 293294326208, subscrevendo para atualizações
2025-09-05 10:00:19.428 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-05 10:00:19.459 -03:00 [INF] Contract 293294326188 near expiry: 2s
2025-09-05 10:00:19.563 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294326188, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:19.564 -03:00 [INF] Contract 293294326208 near expiry: 2s
2025-09-05 10:00:19.564 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294326208, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:20.412 -03:00 [INF] [DEBUG] entry_tick for 293294326188: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:20.413 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326188: 5901,251 at 13:00:20
2025-09-05 10:00:20.413 -03:00 [INF] Contract 293294326188 near expiry: 1s
2025-09-05 10:00:20.415 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294326188, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:20.416 -03:00 [INF] [DEBUG] entry_tick for 293294319968: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:20.416 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319968: 5900,863 at 13:00:16
2025-09-05 10:00:20.417 -03:00 [INF] [DEBUG] entry_tick for 293294319988: epoch=1757077216 -> 13:00:16.000, price=5900,863
2025-09-05 10:00:20.417 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294319988: 5900,863 at 13:00:16
2025-09-05 10:00:20.422 -03:00 [INF] [DEBUG] entry_tick for 293294326208: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:20.423 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326208: 5901,251 at 13:00:20
2025-09-05 10:00:20.423 -03:00 [INF] Contract 293294326208 near expiry: 1s
2025-09-05 10:00:20.426 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294326208, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:20.426 -03:00 [INF] [DEBUG] entry_tick for 293294326208: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:20.426 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326208: 5901,251 at 13:00:20
2025-09-05 10:00:20.427 -03:00 [INF] Contract 293294326208 near expiry: 1s
2025-09-05 10:00:20.427 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294326208, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:20.427 -03:00 [INF] [DEBUG] entry_tick for 293294326188: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:20.428 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326188: 5901,251 at 13:00:20
2025-09-05 10:00:20.428 -03:00 [INF] Contract 293294326188 near expiry: 1s
2025-09-05 10:00:20.428 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294326188, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:22.414 -03:00 [INF] [DEBUG] entry_tick for 293294326188: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:22.414 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326188: 5901,251 at 13:00:20
2025-09-05 10:00:22.414 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294326188 expirado às 10:00:22.414 - Profit: 0,46, Win: True
2025-09-05 10:00:22.414 -03:00 [INF] [DEBUG] exit_time for 293294326188: date_expiry_epoch=1757077222 -> 13:00:22.000
2025-09-05 10:00:22.415 -03:00 [INF] [DUAL] Contract result received at 10:00:22.415 - WIN: True
2025-09-05 10:00:22.415 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-05 10:00:22.415 -03:00 [INF] [DUAL] Contracts completed: 1/2
2025-09-05 10:00:22.415 -03:00 [INF] Profit Table updated for contract 293294326188: Profit=0,46, ExitPrice=5901,202, ExitTime=13:00:22
2025-09-05 10:00:22.415 -03:00 [INF] [DUAL] Contract 2 finished: Stake=0,50, Profit=0,46
2025-09-05 10:00:22.416 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326188: 5901,251 at 13:00:20
2025-09-05 10:00:22.416 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 1,3225ms
2025-09-05 10:00:22.426 -03:00 [INF] [DEBUG] entry_tick for 293294326208: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:22.426 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326208: 5901,251 at 13:00:20
2025-09-05 10:00:22.426 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294326208 expirado às 10:00:22.426 - Profit: -0,35, Win: False
2025-09-05 10:00:22.426 -03:00 [INF] [DEBUG] exit_time for 293294326208: date_expiry_epoch=1757077222 -> 13:00:22.000
2025-09-05 10:00:22.434 -03:00 [INF] [DUAL] Contract result received at 10:00:22.434 - WIN: False
2025-09-05 10:00:22.434 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-05 10:00:22.434 -03:00 [INF] [DUAL] Contracts completed: 2/2
2025-09-05 10:00:22.434 -03:00 [INF] [DUAL] Both contracts completed, processing level
2025-09-05 10:00:22.434 -03:00 [INF] [DUAL] Processing level 4 complete. Session profit: 0,00
2025-09-05 10:00:22.434 -03:00 [INF] [DUAL] Advancing to level 5. Executing next dual entry in 2 seconds...
2025-09-05 10:00:22.435 -03:00 [INF] Profit Table updated for contract 293294326208: Profit=-0,35, ExitPrice=5901,202, ExitTime=13:00:22
2025-09-05 10:00:22.436 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326208: 5901,251 at 13:00:20
2025-09-05 10:00:22.436 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 2,179ms
2025-09-05 10:00:22.436 -03:00 [INF] [DEBUG] entry_tick for 293294326188: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:22.440 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326188: 5901,251 at 13:00:20
2025-09-05 10:00:22.440 -03:00 [INF] [DEBUG] entry_tick for 293294326208: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:22.440 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326208: 5901,251 at 13:00:20
2025-09-05 10:00:24.387 -03:00 [INF] [DEBUG] entry_tick for 293294326208: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:24.387 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326208: 5901,251 at 13:00:20
2025-09-05 10:00:24.387 -03:00 [INF] [DEBUG] entry_tick for 293294326188: epoch=1757077220 -> 13:00:20.000, price=5901,251
2025-09-05 10:00:24.387 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294326188: 5901,251 at 13:00:20
2025-09-05 10:00:24.434 -03:00 [INF] [DUAL] Auto-executing next level 5
2025-09-05 10:00:24.434 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 5/5, Session 1/10000
2025-09-05 10:00:24.434 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:24.756 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=49015885-057a-9d8c-8538-cbd367b11fbf, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:24.756 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:25.016 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=c3fa5414-d1c4-78b1-14f2-029f296d4b58, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:25.016 -03:00 [INF] [DUAL] Payouts obtidos - Contrato 1: 0,66, Contrato 2: 0,66
2025-09-05 10:00:25.016 -03:00 [INF] [DUAL] ENTRADA SUBSEQUENTE - Nível 5
2025-09-05 10:00:25.016 -03:00 [INF] [DUAL] Prejuízo acumulado a recuperar: 0,00
2025-09-05 10:00:25.016 -03:00 [INF] [DUAL] SessionProfit atual: 0,00
2025-09-05 10:00:25.017 -03:00 [INF] [DUAL] Sem prejuízo acumulado, calculando para Take Profit 0,03: higherStake=0,43
2025-09-05 10:00:25.017 -03:00 [INF] [DUAL] Após recuperar prejuízo, SessionProfit seria 0,00, precisando de mais 0,03 para Take Profit
2025-09-05 10:00:25.017 -03:00 [INF] [DUAL] Reajustando stake maior para 0,43 para atingir Take Profit total
2025-09-05 10:00:25.017 -03:00 [INF] [DUAL] Stakes finais - Maior: 0,50, Menor: 0,35
2025-09-05 10:00:25.017 -03:00 [INF] [DUAL] Entrada subsequente - Stake MAIOR vai para: Contrato 2 (que perdeu na rodada anterior)
2025-09-05 10:00:25.017 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0,35, Lower: 0,50
2025-09-05 10:00:25.017 -03:00 [INF] [DUAL] Stake MAIOR (0,50) vai para: Lower
2025-09-05 10:00:25.017 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:25.380 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=49015885-057a-9d8c-8538-cbd367b11fbf, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:25.380 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,50
2025-09-05 10:00:25.576 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=2cc3329f-9a5a-7499-2adc-05548c722868, AskPrice=0,50, Payout=0,96
2025-09-05 10:00:25.576 -03:00 [INF] [DUAL] Payouts - Contrato 1: 0,66, Contrato 2: 0,96
2025-09-05 10:00:25.576 -03:00 [INF] [DUAL] Executando compras - Proposal1: 49015885-057a-9d8c-8538-cbd367b11fbf, Price1: 0,35
2025-09-05 10:00:25.576 -03:00 [INF] [DUAL] Executando compras - Proposal2: 2cc3329f-9a5a-7499-2adc-05548c722868, Price2: 0,50
2025-09-05 10:00:25.818 -03:00 [INF] [DEBUG] Contrato comprado: 293294334608, subscrevendo para atualizações
2025-09-05 10:00:25.818 -03:00 [INF] [DEBUG] Contrato comprado: 293294334588, subscrevendo para atualizações
2025-09-05 10:00:25.819 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-05 10:00:25.856 -03:00 [INF] Contract 293294334588 near expiry: 2s
2025-09-05 10:00:26.083 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294334588, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:26.427 -03:00 [INF] [DEBUG] entry_tick for 293294334588: epoch=1757077226 -> 13:00:26.000, price=5901,28
2025-09-05 10:00:26.428 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294334588: 5901,28 at 13:00:26
2025-09-05 10:00:26.428 -03:00 [INF] Contract 293294334588 near expiry: 1s
2025-09-05 10:00:26.432 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294334588, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:26.433 -03:00 [INF] [DEBUG] entry_tick for 293294334588: epoch=1757077226 -> 13:00:26.000, price=5901,28
2025-09-05 10:00:26.433 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294334588: 5901,28 at 13:00:26
2025-09-05 10:00:26.433 -03:00 [INF] Contract 293294334588 near expiry: 1s
2025-09-05 10:00:26.433 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294334588, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:26.443 -03:00 [INF] [DEBUG] entry_tick for 293294334608: epoch=1757077226 -> 13:00:26.000, price=5901,28
2025-09-05 10:00:26.444 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294334608: 5901,28 at 13:00:26
2025-09-05 10:00:26.445 -03:00 [INF] Contract 293294334608 near expiry: 1s
2025-09-05 10:00:26.448 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294334608, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:28.408 -03:00 [INF] [DEBUG] entry_tick for 293294334588: epoch=1757077226 -> 13:00:26.000, price=5901,28
2025-09-05 10:00:28.408 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294334588: 5901,28 at 13:00:26
2025-09-05 10:00:28.408 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294334588 expirado às 10:00:28.408 - Profit: 0,31, Win: True
2025-09-05 10:00:28.408 -03:00 [INF] [DEBUG] exit_time for 293294334588: date_expiry_epoch=1757077228 -> 13:00:28.000
2025-09-05 10:00:28.408 -03:00 [INF] [DUAL] Contract result received at 10:00:28.408 - WIN: True
2025-09-05 10:00:28.408 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-05 10:00:28.408 -03:00 [INF] [DUAL] Contracts completed: 1/2
2025-09-05 10:00:28.409 -03:00 [INF] Profit Table updated for contract 293294334588: Profit=0,31, ExitPrice=5901,414, ExitTime=13:00:28
2025-09-05 10:00:28.409 -03:00 [INF] [DUAL] Contract 1 finished: Stake=0,35, Profit=0,31
2025-09-05 10:00:28.410 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294334588: 5901,28 at 13:00:26
2025-09-05 10:00:28.410 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 1,556ms
2025-09-05 10:00:28.410 -03:00 [INF] [DEBUG] entry_tick for 293294334608: epoch=1757077226 -> 13:00:26.000, price=5901,28
2025-09-05 10:00:28.414 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294334608: 5901,28 at 13:00:26
2025-09-05 10:00:28.414 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294334608 expirado às 10:00:28.414 - Profit: -0,5, Win: False
2025-09-05 10:00:28.415 -03:00 [INF] [DEBUG] exit_time for 293294334608: date_expiry_epoch=1757077228 -> 13:00:28.000
2025-09-05 10:00:28.415 -03:00 [INF] [DUAL] Contract result received at 10:00:28.415 - WIN: False
2025-09-05 10:00:28.415 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-05 10:00:28.415 -03:00 [INF] [DUAL] Contracts completed: 2/2
2025-09-05 10:00:28.415 -03:00 [INF] [DUAL] Both contracts completed, processing level
2025-09-05 10:00:28.415 -03:00 [INF] [DUAL] Processing level 6 complete. Session profit: 0,00
2025-09-05 10:00:28.415 -03:00 [INF] [DUAL] Maximum levels (5) reached, completing session
2025-09-05 10:00:28.417 -03:00 [INF] [DUAL] Completing session 1. Session profit: 0,00
2025-09-05 10:00:28.417 -03:00 [INF] [DUAL] Total Profit updated: 0,00 + 0,00 = 0,00
2025-09-05 10:00:28.419 -03:00 [INF] [DUAL] Starting new session 2
2025-09-05 10:00:28.447 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 31,7862ms
2025-09-05 10:00:28.447 -03:00 [INF] [DEBUG] entry_tick for 293294334588: epoch=1757077226 -> 13:00:26.000, price=5901,28
2025-09-05 10:00:30.440 -03:00 [INF] [DEBUG] entry_tick for 293294334588: epoch=1757077226 -> 13:00:26.000, price=5901,28
2025-09-05 10:00:30.445 -03:00 [INF] [DEBUG] entry_tick for 293294334608: epoch=1757077226 -> 13:00:26.000, price=5901,28
2025-09-05 10:00:30.470 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 1/5, Session 2/10000
2025-09-05 10:00:30.472 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:30.668 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=24186c17-399d-e003-4dd5-9ec9302c908e, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:30.668 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:30.852 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=c3fa5414-d1c4-78b1-14f2-029f296d4b58, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:30.852 -03:00 [INF] [DUAL] Payouts obtidos - Contrato 1: 0,66, Contrato 2: 0,66
2025-09-05 10:00:30.852 -03:00 [INF] [DUAL] PRIMEIRA ENTRADA - Stake menor: 0,35 (campo Stake), Stake maior: 0,50 (calculada para Take Profit: 0,03)
2025-09-05 10:00:30.853 -03:00 [INF] [DUAL] Cálculo: higherStake1=0,43, higherStake2=0,43, média=0,50
2025-09-05 10:00:30.853 -03:00 [INF] [DUAL] Primeira entrada - Random choice: Contrato principal MENOR stake
2025-09-05 10:00:30.853 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0,35, Lower: 0,50
2025-09-05 10:00:30.853 -03:00 [INF] [DUAL] Stake MAIOR (0,50) vai para: Lower
2025-09-05 10:00:30.853 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:31.046 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=24186c17-399d-e003-4dd5-9ec9302c908e, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:31.046 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,50
2025-09-05 10:00:31.234 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=aa55a3c9-d270-85b9-fa29-caebab87905a, AskPrice=0,50, Payout=0,96
2025-09-05 10:00:31.234 -03:00 [INF] [DUAL] Payouts - Contrato 1: 0,66, Contrato 2: 0,96
2025-09-05 10:00:31.235 -03:00 [INF] [DUAL] Executando compras - Proposal1: 24186c17-399d-e003-4dd5-9ec9302c908e, Price1: 0,35
2025-09-05 10:00:31.235 -03:00 [INF] [DUAL] Executando compras - Proposal2: aa55a3c9-d270-85b9-fa29-caebab87905a, Price2: 0,50
2025-09-05 10:00:31.467 -03:00 [INF] [DEBUG] Contrato comprado: 293294341708, subscrevendo para atualizações
2025-09-05 10:00:31.467 -03:00 [INF] Contract 293294341708 near expiry: 2s
2025-09-05 10:00:31.467 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294341708, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:31.485 -03:00 [INF] [DEBUG] Contrato comprado: 293294341748, subscrevendo para atualizações
2025-09-05 10:00:31.485 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-05 10:00:31.495 -03:00 [INF] Contract 293294341748 near expiry: 2s
2025-09-05 10:00:31.588 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294341748, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:32.481 -03:00 [INF] [DEBUG] entry_tick for 293294341748: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:32.481 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341748: 5901,51 at 13:00:32
2025-09-05 10:00:32.481 -03:00 [INF] Contract 293294341748 near expiry: 1s
2025-09-05 10:00:32.484 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294341748, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:32.485 -03:00 [INF] [DEBUG] entry_tick for 293294341708: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:32.485 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341708: 5901,51 at 13:00:32
2025-09-05 10:00:32.485 -03:00 [INF] Contract 293294341708 near expiry: 1s
2025-09-05 10:00:32.487 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294341708, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:32.488 -03:00 [INF] [DEBUG] entry_tick for 293294341748: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:32.488 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341748: 5901,51 at 13:00:32
2025-09-05 10:00:32.488 -03:00 [INF] Contract 293294341748 near expiry: 1s
2025-09-05 10:00:32.488 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294341748, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:32.488 -03:00 [INF] [DEBUG] entry_tick for 293294341708: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:32.497 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341708: 5901,51 at 13:00:32
2025-09-05 10:00:32.497 -03:00 [INF] Contract 293294341708 near expiry: 1s
2025-09-05 10:00:32.499 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294341708, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:34.468 -03:00 [INF] [DEBUG] entry_tick for 293294341748: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:34.468 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341748: 5901,51 at 13:00:32
2025-09-05 10:00:34.469 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294341748 expirado às 10:00:34.469 - Profit: 0,46, Win: True
2025-09-05 10:00:34.469 -03:00 [INF] [DEBUG] exit_time for 293294341748: date_expiry_epoch=1757077234 -> 13:00:34.000
2025-09-05 10:00:34.469 -03:00 [INF] [DUAL] Contract result received at 10:00:34.469 - WIN: True
2025-09-05 10:00:34.469 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-05 10:00:34.469 -03:00 [INF] [DUAL] Contracts completed: 1/2
2025-09-05 10:00:34.469 -03:00 [INF] Profit Table updated for contract 293294341748: Profit=0,46, ExitPrice=5901,477, ExitTime=13:00:34
2025-09-05 10:00:34.469 -03:00 [INF] [DUAL] Contract 2 finished: Stake=0,50, Profit=0,46
2025-09-05 10:00:34.474 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341748: 5901,51 at 13:00:32
2025-09-05 10:00:34.474 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 5,1152ms
2025-09-05 10:00:34.474 -03:00 [INF] [DEBUG] entry_tick for 293294341748: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:34.474 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341748: 5901,51 at 13:00:32
2025-09-05 10:00:34.491 -03:00 [INF] [DEBUG] entry_tick for 293294341708: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:34.491 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341708: 5901,51 at 13:00:32
2025-09-05 10:00:34.491 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294341708 expirado às 10:00:34.491 - Profit: -0,35, Win: False
2025-09-05 10:00:34.491 -03:00 [INF] [DEBUG] exit_time for 293294341708: date_expiry_epoch=1757077234 -> 13:00:34.000
2025-09-05 10:00:34.492 -03:00 [INF] [DUAL] Contract result received at 10:00:34.492 - WIN: False
2025-09-05 10:00:34.492 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-05 10:00:34.492 -03:00 [INF] [DUAL] Contracts completed: 2/2
2025-09-05 10:00:34.492 -03:00 [INF] [DUAL] Both contracts completed, processing level
2025-09-05 10:00:34.492 -03:00 [INF] [DUAL] Processing level 2 complete. Session profit: 0,00
2025-09-05 10:00:34.492 -03:00 [INF] [DUAL] Advancing to level 3. Executing next dual entry in 2 seconds...
2025-09-05 10:00:34.493 -03:00 [INF] Profit Table updated for contract 293294341708: Profit=-0,35, ExitPrice=5901,477, ExitTime=13:00:34
2025-09-05 10:00:34.493 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341708: 5901,51 at 13:00:32
2025-09-05 10:00:34.493 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 1,4066ms
2025-09-05 10:00:34.493 -03:00 [INF] [DEBUG] entry_tick for 293294341708: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:34.500 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341708: 5901,51 at 13:00:32
2025-09-05 10:00:36.399 -03:00 [INF] [DEBUG] entry_tick for 293294341748: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:36.399 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341748: 5901,51 at 13:00:32
2025-09-05 10:00:36.424 -03:00 [INF] [DEBUG] entry_tick for 293294341708: epoch=1757077232 -> 13:00:32.000, price=5901,51
2025-09-05 10:00:36.425 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294341708: 5901,51 at 13:00:32
2025-09-05 10:00:36.494 -03:00 [INF] [DUAL] Auto-executing next level 3
2025-09-05 10:00:36.494 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 3/5, Session 2/10000
2025-09-05 10:00:36.494 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:36.675 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=4b092b05-0a48-9a3e-2770-832a1cb5cbab, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:36.675 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=c3fa5414-d1c4-78b1-14f2-029f296d4b58, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] Payouts obtidos - Contrato 1: 0,66, Contrato 2: 0,66
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] ENTRADA SUBSEQUENTE - Nível 3
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] Prejuízo acumulado a recuperar: 0,00
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] SessionProfit atual: 0,00
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] Sem prejuízo acumulado, calculando para Take Profit 0,03: higherStake=0,43
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] Após recuperar prejuízo, SessionProfit seria 0,00, precisando de mais 0,03 para Take Profit
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] Reajustando stake maior para 0,43 para atingir Take Profit total
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] Stakes finais - Maior: 0,50, Menor: 0,35
2025-09-05 10:00:36.850 -03:00 [INF] [DUAL] Entrada subsequente - Stake MAIOR vai para: Contrato 2 (que perdeu na rodada anterior)
2025-09-05 10:00:36.851 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0,35, Lower: 0,50
2025-09-05 10:00:36.851 -03:00 [INF] [DUAL] Stake MAIOR (0,50) vai para: Lower
2025-09-05 10:00:36.851 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:37.028 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=4b092b05-0a48-9a3e-2770-832a1cb5cbab, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:37.028 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,50
2025-09-05 10:00:37.211 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=99cc825b-f5cc-5bcc-a7ac-430758ec1302, AskPrice=0,50, Payout=0,96
2025-09-05 10:00:37.212 -03:00 [INF] [DUAL] Payouts - Contrato 1: 0,66, Contrato 2: 0,96
2025-09-05 10:00:37.212 -03:00 [INF] [DUAL] Executando compras - Proposal1: 4b092b05-0a48-9a3e-2770-832a1cb5cbab, Price1: 0,35
2025-09-05 10:00:37.212 -03:00 [INF] [DUAL] Executando compras - Proposal2: 99cc825b-f5cc-5bcc-a7ac-430758ec1302, Price2: 0,50
2025-09-05 10:00:37.440 -03:00 [INF] [DEBUG] Contrato comprado: 293294349248, subscrevendo para atualizações
2025-09-05 10:00:37.446 -03:00 [INF] Contract 293294349248 near expiry: 2s
2025-09-05 10:00:37.446 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294349248, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:37.447 -03:00 [INF] [DEBUG] Contrato comprado: 293294349228, subscrevendo para atualizações
2025-09-05 10:00:37.447 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-05 10:00:37.465 -03:00 [INF] Contract 293294349228 near expiry: 2s
2025-09-05 10:00:37.595 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294349228, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:38.382 -03:00 [INF] [DEBUG] entry_tick for 293294349228: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:38.383 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349228: 5901,896 at 13:00:38
2025-09-05 10:00:38.383 -03:00 [INF] Contract 293294349228 near expiry: 1s
2025-09-05 10:00:38.387 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294349228, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:38.389 -03:00 [INF] [DEBUG] entry_tick for 293294349228: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:38.390 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349228: 5901,896 at 13:00:38
2025-09-05 10:00:38.390 -03:00 [INF] Contract 293294349228 near expiry: 1s
2025-09-05 10:00:38.390 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294349228, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:38.396 -03:00 [INF] [DEBUG] entry_tick for 293294349248: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:38.397 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349248: 5901,896 at 13:00:38
2025-09-05 10:00:38.397 -03:00 [INF] Contract 293294349248 near expiry: 1s
2025-09-05 10:00:38.400 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294349248, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:38.401 -03:00 [INF] [DEBUG] entry_tick for 293294349248: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:38.401 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349248: 5901,896 at 13:00:38
2025-09-05 10:00:38.401 -03:00 [INF] Contract 293294349248 near expiry: 1s
2025-09-05 10:00:38.401 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294349248, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:40.511 -03:00 [INF] [DEBUG] entry_tick for 293294349228: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:40.511 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349228: 5901,896 at 13:00:38
2025-09-05 10:00:40.512 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294349228 expirado às 10:00:40.512 - Profit: -0,5, Win: False
2025-09-05 10:00:40.512 -03:00 [INF] [DEBUG] exit_time for 293294349228: date_expiry_epoch=1757077240 -> 13:00:40.000
2025-09-05 10:00:40.512 -03:00 [INF] [DUAL] Contract result received at 10:00:40.512 - WIN: False
2025-09-05 10:00:40.512 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-05 10:00:40.512 -03:00 [INF] [DUAL] Contracts completed: 1/2
2025-09-05 10:00:40.513 -03:00 [INF] Profit Table updated for contract 293294349228: Profit=-0,5, ExitPrice=5901,934, ExitTime=13:00:40
2025-09-05 10:00:40.513 -03:00 [INF] [DUAL] Contract 2 finished: Stake=0,50, Profit=-0,50
2025-09-05 10:00:40.520 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349228: 5901,896 at 13:00:38
2025-09-05 10:00:40.520 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 8,0323ms
2025-09-05 10:00:40.520 -03:00 [INF] [DEBUG] entry_tick for 293294349228: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:40.520 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349228: 5901,896 at 13:00:38
2025-09-05 10:00:40.521 -03:00 [INF] [DEBUG] entry_tick for 293294349248: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:40.521 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349248: 5901,896 at 13:00:38
2025-09-05 10:00:40.521 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294349248 expirado às 10:00:40.521 - Profit: 0,31, Win: True
2025-09-05 10:00:40.521 -03:00 [INF] [DEBUG] exit_time for 293294349248: date_expiry_epoch=1757077240 -> 13:00:40.000
2025-09-05 10:00:40.521 -03:00 [INF] [DUAL] Contract result received at 10:00:40.521 - WIN: True
2025-09-05 10:00:40.521 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-05 10:00:40.521 -03:00 [INF] [DUAL] Contracts completed: 2/2
2025-09-05 10:00:40.521 -03:00 [INF] [DUAL] Both contracts completed, processing level
2025-09-05 10:00:40.521 -03:00 [INF] [DUAL] Processing level 4 complete. Session profit: 0,00
2025-09-05 10:00:40.522 -03:00 [INF] [DUAL] Advancing to level 5. Executing next dual entry in 2 seconds...
2025-09-05 10:00:40.522 -03:00 [INF] Profit Table updated for contract 293294349248: Profit=0,31, ExitPrice=5901,934, ExitTime=13:00:40
2025-09-05 10:00:40.523 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349248: 5901,896 at 13:00:38
2025-09-05 10:00:40.523 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 1,3892ms
2025-09-05 10:00:40.523 -03:00 [INF] [DEBUG] entry_tick for 293294349248: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:40.568 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349248: 5901,896 at 13:00:38
2025-09-05 10:00:42.342 -03:00 [INF] [DEBUG] entry_tick for 293294349228: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:42.343 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349228: 5901,896 at 13:00:38
2025-09-05 10:00:42.344 -03:00 [INF] [DEBUG] entry_tick for 293294349248: epoch=1757077238 -> 13:00:38.000, price=5901,896
2025-09-05 10:00:42.346 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294349248: 5901,896 at 13:00:38
2025-09-05 10:00:42.522 -03:00 [INF] [DUAL] Auto-executing next level 5
2025-09-05 10:00:42.522 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 5/5, Session 2/10000
2025-09-05 10:00:42.522 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:42.700 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=b68cbd21-5452-6c0a-8eb0-9c564aef452a, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:42.700 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:42.881 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=c3fa5414-d1c4-78b1-14f2-029f296d4b58, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Payouts obtidos - Contrato 1: 0,66, Contrato 2: 0,66
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] ENTRADA SUBSEQUENTE - Nível 5
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Prejuízo acumulado a recuperar: 0,00
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] SessionProfit atual: 0,00
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Sem prejuízo acumulado, calculando para Take Profit 0,03: higherStake=0,43
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Após recuperar prejuízo, SessionProfit seria 0,00, precisando de mais 0,03 para Take Profit
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Reajustando stake maior para 0,43 para atingir Take Profit total
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Stakes finais - Maior: 0,50, Menor: 0,35
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Entrada subsequente - Stake MAIOR vai para: Contrato 2 (que perdeu na rodada anterior)
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0,35, Lower: 0,50
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Stake MAIOR (0,50) vai para: Lower
2025-09-05 10:00:42.882 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:43.128 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=b68cbd21-5452-6c0a-8eb0-9c564aef452a, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:43.128 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,50
2025-09-05 10:00:43.322 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=8d5fdfdd-6280-8005-bb4d-025ab8e30746, AskPrice=0,50, Payout=0,96
2025-09-05 10:00:43.322 -03:00 [INF] [DUAL] Payouts - Contrato 1: 0,66, Contrato 2: 0,96
2025-09-05 10:00:43.322 -03:00 [INF] [DUAL] Executando compras - Proposal1: b68cbd21-5452-6c0a-8eb0-9c564aef452a, Price1: 0,35
2025-09-05 10:00:43.322 -03:00 [INF] [DUAL] Executando compras - Proposal2: 8d5fdfdd-6280-8005-bb4d-025ab8e30746, Price2: 0,50
2025-09-05 10:00:43.554 -03:00 [INF] [DEBUG] Contrato comprado: 293294357268, subscrevendo para atualizações
2025-09-05 10:00:43.584 -03:00 [INF] Contract 293294357268 near expiry: 2s
2025-09-05 10:00:43.589 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294357268, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:43.590 -03:00 [INF] Contract 293294357308 near expiry: 2s
2025-09-05 10:00:43.590 -03:00 [INF] [DEBUG] Contrato comprado: 293294357308, subscrevendo para atualizações
2025-09-05 10:00:43.590 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294357308, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:43.590 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-05 10:00:44.408 -03:00 [INF] [DEBUG] entry_tick for 293294357308: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:44.408 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294357308: 5901,721 at 13:00:44
2025-09-05 10:00:44.409 -03:00 [INF] Contract 293294357308 near expiry: 1s
2025-09-05 10:00:44.412 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294357308, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:44.413 -03:00 [INF] [DEBUG] entry_tick for 293294357268: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:44.413 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294357268: 5901,721 at 13:00:44
2025-09-05 10:00:44.413 -03:00 [INF] Contract 293294357268 near expiry: 1s
2025-09-05 10:00:44.417 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294357268, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:44.423 -03:00 [INF] [DEBUG] entry_tick for 293294357268: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:44.424 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294357268: 5901,721 at 13:00:44
2025-09-05 10:00:44.424 -03:00 [INF] Contract 293294357268 near expiry: 1s
2025-09-05 10:00:44.424 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294357268, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:44.424 -03:00 [INF] [DEBUG] entry_tick for 293294357308: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:44.425 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294357308: 5901,721 at 13:00:44
2025-09-05 10:00:44.425 -03:00 [INF] Contract 293294357308 near expiry: 1s
2025-09-05 10:00:44.425 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294357308, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:46.484 -03:00 [INF] [DEBUG] entry_tick for 293294357268: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:46.484 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294357268: 5901,721 at 13:00:44
2025-09-05 10:00:46.484 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294357268 expirado às 10:00:46.484 - Profit: -0,35, Win: False
2025-09-05 10:00:46.484 -03:00 [INF] [DEBUG] exit_time for 293294357268: date_expiry_epoch=1757077246 -> 13:00:46.000
2025-09-05 10:00:46.484 -03:00 [INF] [DUAL] Contract result received at 10:00:46.484 - WIN: False
2025-09-05 10:00:46.484 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-05 10:00:46.484 -03:00 [INF] [DUAL] Contracts completed: 1/2
2025-09-05 10:00:46.485 -03:00 [INF] Profit Table updated for contract 293294357268: Profit=-0,35, ExitPrice=5901,603, ExitTime=13:00:46
2025-09-05 10:00:46.485 -03:00 [INF] [DUAL] Contract 1 finished: Stake=0,35, Profit=-0,35
2025-09-05 10:00:46.490 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294357268: 5901,721 at 13:00:44
2025-09-05 10:00:46.490 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 5,3695ms
2025-09-05 10:00:46.490 -03:00 [INF] [DEBUG] entry_tick for 293294357268: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:46.490 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294357268: 5901,721 at 13:00:44
2025-09-05 10:00:46.493 -03:00 [INF] [DEBUG] entry_tick for 293294357308: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:46.494 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294357308: 5901,721 at 13:00:44
2025-09-05 10:00:46.494 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294357308 expirado às 10:00:46.494 - Profit: 0,46, Win: True
2025-09-05 10:00:46.495 -03:00 [INF] [DEBUG] exit_time for 293294357308: date_expiry_epoch=1757077246 -> 13:00:46.000
2025-09-05 10:00:46.495 -03:00 [INF] [DUAL] Contract result received at 10:00:46.495 - WIN: True
2025-09-05 10:00:46.495 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-05 10:00:46.495 -03:00 [INF] [DUAL] Contracts completed: 2/2
2025-09-05 10:00:46.495 -03:00 [INF] [DUAL] Both contracts completed, processing level
2025-09-05 10:00:46.495 -03:00 [INF] [DUAL] Processing level 6 complete. Session profit: 0,00
2025-09-05 10:00:46.495 -03:00 [INF] [DUAL] Maximum levels (5) reached, completing session
2025-09-05 10:00:46.495 -03:00 [INF] [DUAL] Completing session 2. Session profit: 0,00
2025-09-05 10:00:46.495 -03:00 [INF] [DUAL] Total Profit updated: 0,00 + 0,00 = 0,00
2025-09-05 10:00:46.495 -03:00 [INF] [DUAL] Starting new session 3
2025-09-05 10:00:46.509 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 14,3491ms
2025-09-05 10:00:46.509 -03:00 [INF] [DEBUG] entry_tick for 293294357308: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:48.418 -03:00 [INF] [DEBUG] entry_tick for 293294357308: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:48.418 -03:00 [INF] [DEBUG] entry_tick for 293294357268: epoch=1757077244 -> 13:00:44.000, price=5901,721
2025-09-05 10:00:48.508 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 1/5, Session 3/10000
2025-09-05 10:00:48.508 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:48.688 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=bcfa3f2d-7a38-8c2a-363e-f9d64e34edaf, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:48.688 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:48.904 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=c3fa5414-d1c4-78b1-14f2-029f296d4b58, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:48.904 -03:00 [INF] [DUAL] Payouts obtidos - Contrato 1: 0,66, Contrato 2: 0,66
2025-09-05 10:00:48.904 -03:00 [INF] [DUAL] PRIMEIRA ENTRADA - Stake menor: 0,35 (campo Stake), Stake maior: 0,50 (calculada para Take Profit: 0,03)
2025-09-05 10:00:48.904 -03:00 [INF] [DUAL] Cálculo: higherStake1=0,43, higherStake2=0,43, média=0,50
2025-09-05 10:00:48.904 -03:00 [INF] [DUAL] Primeira entrada - Random choice: Contrato principal MAIOR stake
2025-09-05 10:00:48.904 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0,50, Lower: 0,35
2025-09-05 10:00:48.904 -03:00 [INF] [DUAL] Stake MAIOR (0,50) vai para: Higher
2025-09-05 10:00:48.904 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,50
2025-09-05 10:00:49.086 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=fa9a769d-cbfa-f690-1aba-0b8d8c6adc64, AskPrice=0,50, Payout=0,96
2025-09-05 10:00:49.086 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:49.286 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=c3fa5414-d1c4-78b1-14f2-029f296d4b58, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:49.286 -03:00 [INF] [DUAL] Payouts - Contrato 1: 0,96, Contrato 2: 0,66
2025-09-05 10:00:49.286 -03:00 [INF] [DUAL] Executando compras - Proposal1: fa9a769d-cbfa-f690-1aba-0b8d8c6adc64, Price1: 0,50
2025-09-05 10:00:49.286 -03:00 [INF] [DUAL] Executando compras - Proposal2: c3fa5414-d1c4-78b1-14f2-029f296d4b58, Price2: 0,35
2025-09-05 10:00:49.552 -03:00 [INF] [DEBUG] Contrato comprado: 293294364928, subscrevendo para atualizações
2025-09-05 10:00:49.552 -03:00 [INF] Contract 293294364928 near expiry: 2s
2025-09-05 10:00:49.553 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294364928, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:49.554 -03:00 [INF] [DEBUG] Contrato comprado: 293294364908, subscrevendo para atualizações
2025-09-05 10:00:49.554 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-05 10:00:49.558 -03:00 [INF] Contract 293294364908 near expiry: 2s
2025-09-05 10:00:49.639 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294364908, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:50.488 -03:00 [INF] [DEBUG] entry_tick for 293294364928: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:50.489 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364928: 5901,666 at 13:00:50
2025-09-05 10:00:50.489 -03:00 [INF] Contract 293294364928 near expiry: 1s
2025-09-05 10:00:50.499 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294364928, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:50.502 -03:00 [INF] [DEBUG] entry_tick for 293294364928: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:50.503 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364928: 5901,666 at 13:00:50
2025-09-05 10:00:50.503 -03:00 [INF] Contract 293294364928 near expiry: 1s
2025-09-05 10:00:50.503 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294364928, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:50.504 -03:00 [INF] [DEBUG] entry_tick for 293294364908: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:50.504 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364908: 5901,666 at 13:00:50
2025-09-05 10:00:50.504 -03:00 [INF] Contract 293294364908 near expiry: 1s
2025-09-05 10:00:50.506 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294364908, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:50.506 -03:00 [INF] [DEBUG] entry_tick for 293294364908: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:50.507 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364908: 5901,666 at 13:00:50
2025-09-05 10:00:50.507 -03:00 [INF] Contract 293294364908 near expiry: 1s
2025-09-05 10:00:50.507 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294364908, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:52.391 -03:00 [INF] [DEBUG] entry_tick for 293294364908: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:52.392 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364908: 5901,666 at 13:00:50
2025-09-05 10:00:52.392 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294364908 expirado às 10:00:52.392 - Profit: 0,46, Win: True
2025-09-05 10:00:52.392 -03:00 [INF] [DEBUG] exit_time for 293294364908: date_expiry_epoch=1757077252 -> 13:00:52.000
2025-09-05 10:00:52.392 -03:00 [INF] [DUAL] Contract result received at 10:00:52.392 - WIN: True
2025-09-05 10:00:52.392 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-05 10:00:52.392 -03:00 [INF] [DUAL] Contracts completed: 1/2
2025-09-05 10:00:52.393 -03:00 [INF] Profit Table updated for contract 293294364908: Profit=0,46, ExitPrice=5901,854, ExitTime=13:00:52
2025-09-05 10:00:52.393 -03:00 [INF] [DUAL] Contract 1 finished: Stake=0,50, Profit=0,46
2025-09-05 10:00:52.396 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364908: 5901,666 at 13:00:50
2025-09-05 10:00:52.396 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 4,2715ms
2025-09-05 10:00:52.401 -03:00 [INF] [DEBUG] entry_tick for 293294364928: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:52.402 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364928: 5901,666 at 13:00:50
2025-09-05 10:00:52.402 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294364928 expirado às 10:00:52.402 - Profit: -0,35, Win: False
2025-09-05 10:00:52.402 -03:00 [INF] [DEBUG] exit_time for 293294364928: date_expiry_epoch=1757077252 -> 13:00:52.000
2025-09-05 10:00:52.402 -03:00 [INF] [DUAL] Contract result received at 10:00:52.402 - WIN: False
2025-09-05 10:00:52.402 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-05 10:00:52.402 -03:00 [INF] [DUAL] Contracts completed: 2/2
2025-09-05 10:00:52.402 -03:00 [INF] [DUAL] Both contracts completed, processing level
2025-09-05 10:00:52.402 -03:00 [INF] [DUAL] Processing level 2 complete. Session profit: 0,00
2025-09-05 10:00:52.402 -03:00 [INF] [DUAL] Advancing to level 3. Executing next dual entry in 2 seconds...
2025-09-05 10:00:52.403 -03:00 [INF] Profit Table updated for contract 293294364928: Profit=-0,35, ExitPrice=5901,854, ExitTime=13:00:52
2025-09-05 10:00:52.409 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364928: 5901,666 at 13:00:50
2025-09-05 10:00:52.409 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 7,2354ms
2025-09-05 10:00:52.409 -03:00 [INF] [DEBUG] entry_tick for 293294364928: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:52.410 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364928: 5901,666 at 13:00:50
2025-09-05 10:00:52.412 -03:00 [INF] [DEBUG] entry_tick for 293294364908: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:52.413 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364908: 5901,666 at 13:00:50
2025-09-05 10:00:54.404 -03:00 [INF] [DUAL] Auto-executing next level 3
2025-09-05 10:00:54.404 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 3/5, Session 3/10000
2025-09-05 10:00:54.404 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:54.412 -03:00 [INF] [DEBUG] entry_tick for 293294364928: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:54.412 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364928: 5901,666 at 13:00:50
2025-09-05 10:00:54.421 -03:00 [INF] [DEBUG] entry_tick for 293294364908: epoch=1757077250 -> 13:00:50.000, price=5901,666
2025-09-05 10:00:54.421 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294364908: 5901,666 at 13:00:50
2025-09-05 10:00:54.590 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=bcfa3f2d-7a38-8c2a-363e-f9d64e34edaf, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:54.590 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,35
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=1b52f53e-0727-7981-2537-46f8227c0278, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Payouts obtidos - Contrato 1: 0,66, Contrato 2: 0,66
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] ENTRADA SUBSEQUENTE - Nível 3
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Prejuízo acumulado a recuperar: 0,00
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] SessionProfit atual: 0,00
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Sem prejuízo acumulado, calculando para Take Profit 0,03: higherStake=0,43
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Após recuperar prejuízo, SessionProfit seria 0,00, precisando de mais 0,03 para Take Profit
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Reajustando stake maior para 0,43 para atingir Take Profit total
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Stakes finais - Maior: 0,50, Menor: 0,35
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Entrada subsequente - Stake MAIOR vai para: Contrato 2 (que perdeu na rodada anterior)
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0,35, Lower: 0,50
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Stake MAIOR (0,50) vai para: Lower
2025-09-05 10:00:54.761 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0,35
2025-09-05 10:00:54.941 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=bcfa3f2d-7a38-8c2a-363e-f9d64e34edaf, AskPrice=0,35, Payout=0,66
2025-09-05 10:00:54.941 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0,50
2025-09-05 10:00:55.174 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=7099edbe-7e0a-9da7-a851-e7cadab2a63e, AskPrice=0,50, Payout=0,96
2025-09-05 10:00:55.174 -03:00 [INF] [DUAL] Payouts - Contrato 1: 0,66, Contrato 2: 0,96
2025-09-05 10:00:55.174 -03:00 [INF] [DUAL] Executando compras - Proposal1: bcfa3f2d-7a38-8c2a-363e-f9d64e34edaf, Price1: 0,35
2025-09-05 10:00:55.174 -03:00 [INF] [DUAL] Executando compras - Proposal2: 7099edbe-7e0a-9da7-a851-e7cadab2a63e, Price2: 0,50
2025-09-05 10:00:55.415 -03:00 [INF] [DEBUG] Contrato comprado: 293294372568, subscrevendo para atualizações
2025-09-05 10:00:55.426 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-05 10:00:55.426 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-05 10:00:55.458 -03:00 [INF] Contract 293294372568 near expiry: 2s
2025-09-05 10:00:55.557 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294372568, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:55.558 -03:00 [INF] Contract ************ near expiry: 2s
2025-09-05 10:00:55.558 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: ************, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:56.476 -03:00 [INF] [DEBUG] entry_tick for 293294372568: epoch=********** -> 13:00:56.000, price=5901,875
2025-09-05 10:00:56.477 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294372568: 5901,875 at 13:00:56
2025-09-05 10:00:56.477 -03:00 [INF] Contract 293294372568 near expiry: 1s
2025-09-05 10:00:56.479 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294372568, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:56.488 -03:00 [INF] [DEBUG] entry_tick for 293294372568: epoch=********** -> 13:00:56.000, price=5901,875
2025-09-05 10:00:56.488 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294372568: 5901,875 at 13:00:56
2025-09-05 10:00:56.488 -03:00 [INF] Contract 293294372568 near expiry: 1s
2025-09-05 10:00:56.488 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: 293294372568, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:56.489 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:00:56.000, price=5901,875
2025-09-05 10:00:56.489 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5901,875 at 13:00:56
2025-09-05 10:00:56.489 -03:00 [INF] Contract ************ near expiry: 1s
2025-09-05 10:00:56.492 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: ************, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:56.492 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:00:56.000, price=5901,875
2025-09-05 10:00:56.493 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5901,875 at 13:00:56
2025-09-05 10:00:56.493 -03:00 [INF] Contract ************ near expiry: 1s
2025-09-05 10:00:56.493 -03:00 [INF] [DEBUG] OnContractNearExpiry chamado para contrato: ************, IsMartingaleEnabled = False, IsFastMartingale = False
2025-09-05 10:00:58.459 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:00:56.000, price=5901,875
2025-09-05 10:00:58.459 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5901,875 at 13:00:56
2025-09-05 10:00:58.459 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato ************ expirado às 10:00:58.459 - Profit: 0,46, Win: True
2025-09-05 10:00:58.459 -03:00 [INF] [DEBUG] exit_time for ************: date_expiry_epoch=1757077258 -> 13:00:58.000
2025-09-05 10:00:58.459 -03:00 [INF] [DUAL] Contract result received at 10:00:58.459 - WIN: True
2025-09-05 10:00:58.459 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-05 10:00:58.459 -03:00 [INF] [DUAL] Contracts completed: 1/2
2025-09-05 10:00:58.460 -03:00 [INF] Profit Table updated for contract ************: Profit=0,46, ExitPrice=5901,826, ExitTime=13:00:58
2025-09-05 10:00:58.460 -03:00 [INF] [DUAL] Contract 2 finished: Stake=0,50, Profit=0,46
2025-09-05 10:00:58.465 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5901,875 at 13:00:56
2025-09-05 10:00:58.465 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 5,8709ms
2025-09-05 10:00:58.470 -03:00 [INF] [DEBUG] entry_tick for 293294372568: epoch=********** -> 13:00:56.000, price=5901,875
2025-09-05 10:00:58.470 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294372568: 5901,875 at 13:00:56
2025-09-05 10:00:58.471 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE - Contrato 293294372568 expirado às 10:00:58.471 - Profit: -0,35, Win: False
2025-09-05 10:00:58.471 -03:00 [INF] [DEBUG] exit_time for 293294372568: date_expiry_epoch=1757077258 -> 13:00:58.000
2025-09-05 10:00:58.471 -03:00 [INF] [DUAL] Contract result received at 10:00:58.471 - WIN: False
2025-09-05 10:00:58.471 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-05 10:00:58.471 -03:00 [INF] [DUAL] Contracts completed: 2/2
2025-09-05 10:00:58.471 -03:00 [INF] [DUAL] Both contracts completed, processing level
2025-09-05 10:00:58.471 -03:00 [INF] [DUAL] Processing level 4 complete. Session profit: 0,00
2025-09-05 10:00:58.471 -03:00 [INF] [DUAL] Advancing to level 5. Executing next dual entry in 2 seconds...
2025-09-05 10:00:58.471 -03:00 [INF] Profit Table updated for contract 293294372568: Profit=-0,35, ExitPrice=5901,826, ExitTime=13:00:58
2025-09-05 10:00:58.474 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294372568: 5901,875 at 13:00:56
2025-09-05 10:00:58.474 -03:00 [INF] [TIMING] ULTRA-IMMEDIATE ContractResult dispatched in 3,549ms
2025-09-05 10:00:58.474 -03:00 [INF] [DEBUG] entry_tick for 293294372568: epoch=********** -> 13:00:56.000, price=5901,875
2025-09-05 10:00:58.475 -03:00 [INF] Profit Table entry updated with official entry tick for contract 293294372568: 5901,875 at 13:00:56
2025-09-05 10:00:58.475 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:00:56.000, price=5901,875
2025-09-05 10:00:58.476 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5901,875 at 13:00:56
2025-09-05 10:00:59.211 -03:00 [INF] Application is shutting down...
2025-09-05 10:00:59.255 -03:00 [ERR] Erro ao processar mensagem da API.
System.NullReferenceException: Object reference not set to an instance of an object.
   at Excalibur.ViewModels.MainViewModel.OnAccountInfoUpdated(String accountCode, String accountType, Double balance) in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 889
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.5\Services\DerivApiService.cs:line 271
2025-09-05 10:00:59.387 -03:00 [ERR] Erro ao processar mensagem da API.
System.NullReferenceException: Object reference not set to an instance of an object.
   at Excalibur.ViewModels.MainViewModel.OnPingUpdated(Int64 newPing) in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 797
   at Excalibur.Services.DerivApiService.ProcessMessage(String jsonMessage) in C:\Users\<USER>\Downloads\excalibur1.5\Services\DerivApiService.cs:line 277
