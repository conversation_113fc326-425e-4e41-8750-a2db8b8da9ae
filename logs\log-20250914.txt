2025-09-14 10:21:47.866 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-14 10:21:47.889 -03:00 [INF] Hosting environment: Production
2025-09-14 10:21:47.891 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.5
2025-09-14 10:21:48.296 -03:00 [INF] [CONFIG] Caminho do arquivo de configuração: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:21:48.307 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 10:21:48.312 -03:00 [INF] [AUTO-PAUSE] Timer de pausa automática inicializado com intervalo de 30 segundos
2025-09-14 10:21:48.312 -03:00 [INF] Conectando à API Deriv...
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 5
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: False
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.00
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] AskPrice: 0
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] CurrentProposalId: 'null'
2025-09-14 10:21:48.739 -03:00 [INF] [DEBUG CanExecuteBuy] Normal Mode Result: False
2025-09-14 10:21:49.253 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-14 10:21:49.327 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:13:13
2025-09-14 10:21:49.335 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:21:49.336 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:21:49.338 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:21:49.338 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:21:49.340 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:21:49.340 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:21:49.340 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:21:49.340 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:21:49.343 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 10:21:49.343 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:21:49.343 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:21:49.343 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:21:49.489 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:21:49.489 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 10:21:49.489 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.01
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: null
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:21:49.490 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: False (allowing automatic continuation)
2025-09-14 10:21:49.639 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:21:49.639 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 0.00
2025-09-14 10:21:49.639 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 0.00
2025-09-14 10:21:49.639 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:21:49.639 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:21:49.640 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:21:49.640 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17714.20
2025-09-14 10:21:49.640 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 17714.20
2025-09-14 10:21:49.640 -03:00 [INF] [BALANCE] Initial balance set to: 17714.20
2025-09-14 10:21:49.641 -03:00 [INF] Status de conexão alterado para: True
2025-09-14 10:21:49.644 -03:00 [INF] [CONNECTION] Connection reestablished - restoring application state
2025-09-14 10:21:49.647 -03:00 [INF] [RESTORE] Step 1: Force loading latest configuration
2025-09-14 10:21:49.647 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 10:21:49.647 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:21:49.647 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:13:13
2025-09-14 10:21:49.647 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:13:13
2025-09-14 10:21:49.647 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:21:49.648 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:21:49.648 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:21:49.648 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:21:49.648 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:21:49.648 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:21:49.648 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:21:49.648 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:21:49.648 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 10:21:49.648 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:21:49.648 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:21:49.648 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:21:49.648 -03:00 [INF] [RESTORE] State to restore - Market: Derived, Symbol: R_10, Contract: CALLE, DualContract: PUTE
2025-09-14 10:21:49.648 -03:00 [INF] [RESTORE] Step 2: Loading active symbols
2025-09-14 10:21:49.648 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-14 10:21:49.705 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:21:49.705 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:21:49.705 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:21:49.705 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:21:49.705 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:21:49.705 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:21:49.955 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:21:49.956 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:21:49
2025-09-14 10:21:50.008 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:21:50.009 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:21:50.009 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:21:50.009 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:21:50.009 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:21:50.009 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:21:50.237 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-14 10:21:50.239 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-14 10:21:50.239 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-14 10:21:50.239 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-14 10:21:50.239 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-14 10:21:50.239 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-14 10:21:50.239 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-14 10:21:50.239 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-14 10:21:50.239 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 10:21:50.242 -03:00 [INF] [CONFIG] Mercado restaurado: Derived
2025-09-14 10:21:50.242 -03:00 [INF] [RESTORE] Step 3: Restoring selections with retry
2025-09-14 10:21:50.243 -03:00 [INF] [RESTORE RETRY] Attempt 1/5 - Starting restoration
2025-09-14 10:21:50.244 -03:00 [INF] [RESTORE] Starting selection restoration - Market: Derived, Symbol: R_10, Contract: CALLE, Dual: PUTE
2025-09-14 10:21:50.254 -03:00 [INF] [RESTORE] Markets loaded successfully: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 10:21:50.255 -03:00 [INF] [RESTORE] ✓ Market restored: 'Derived' -> 'Derived'
2025-09-14 10:21:50.257 -03:00 [INF] [RESTORE] ✓ SubMarket restored: 'Continuous Indices' -> 'Continuous Indices'
2025-09-14 10:21:50.262 -03:00 [INF] [RESTORE] ✓ Symbol restored: 'R_10' -> 'R_10'
2025-09-14 10:21:50.263 -03:00 [INF] [RESTORE] Contract types list empty, waiting... attempt 1/25
2025-09-14 10:21:50.561 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:21:50.561 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:21:49
2025-09-14 10:21:50.563 -03:00 [INF] [RESTORE] Contract types list empty, waiting... attempt 2/25
2025-09-14 10:21:50.614 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:21:50.614 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:21:50.614 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:21:50.614 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:21:50.614 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:21:50.614 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:21:50.865 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:21:50.865 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-14 10:21:50.870 -03:00 [INF] [RESTORE] ✓ Main contract type restored: 'CALLE' (attempt 3)
2025-09-14 10:21:50.871 -03:00 [INF] [RESTORE] ✓ Dual contract type restored: 'PUTE' (attempt 1)
2025-09-14 10:21:50.871 -03:00 [INF] [RESTORE] Final restoration state:
2025-09-14 10:21:50.871 -03:00 [INF] [RESTORE]   Market: 'Derived' -> 'Derived' ✓
2025-09-14 10:21:50.871 -03:00 [INF] [RESTORE]   SubMarket: 'Continuous Indices' -> 'Continuous Indices' ✓
2025-09-14 10:21:50.871 -03:00 [INF] [RESTORE]   Symbol: 'R_10' -> 'R_10' ✓
2025-09-14 10:21:50.871 -03:00 [INF] [RESTORE]   Contract: 'CALLE' -> 'CALLE' ✓
2025-09-14 10:21:50.871 -03:00 [INF] [RESTORE]   DualContract: 'PUTE' -> 'PUTE' ✓
2025-09-14 10:21:50.873 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-14 10:21:51.171 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:21:51.174 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:21:50
2025-09-14 10:21:51.226 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:21:51.227 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:21:51.227 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:21:51.227 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:21:51.227 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:21:51.227 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:21:51.264 -03:00 [INF] [RESTORE VERIFY] Market: True, Symbol: True, Contract: True, DualContract: True
2025-09-14 10:21:51.264 -03:00 [INF] [RESTORE RETRY] ✓ Restoration successful on attempt 1
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.01
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:21:53.663 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.01
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:21:54.678 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.01
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:21:54.888 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:21:54.890 -03:00 [INF] [BUY] ExecuteBuyCommand called - IsDualEnabled: True
2025-09-14 10:21:54.890 -03:00 [INF] [BUY] Dual mode detected, calling ExecuteDualEntryCommand
2025-09-14 10:21:54.894 -03:00 [INF] [DUAL DEBUG] 🚀 ExecuteDualEntryCommand started
2025-09-14 10:21:54.894 -03:00 [INF] [DUAL DEBUG] Connection check passed
2025-09-14 10:21:54.894 -03:00 [INF] [DUAL DEBUG] Timing check bypassed for dual mode (no-delay)
2025-09-14 10:21:54.894 -03:00 [INF] [DUAL DEBUG] Validating contract types - ContractType: CALLE, DualContractType: PUTE, Symbol: R_10
2025-09-14 10:21:54.894 -03:00 [INF] [DUAL] Iniciando primeira sessão dual - Session 1
2025-09-14 10:21:54.894 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 0/5, Session 1/1
2025-09-14 10:21:54.895 -03:00 [INF] [NEW_DUAL] 🚀 Chamando CalculateNewDualStakes...
2025-09-14 10:21:54.896 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:21:54.896 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:21:54.896 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 10:21:54.896 -03:00 [INF] [NEW_DUAL] ✅ Calculated stakes using new formulas - X: 0.35, Y: 0.60
2025-09-14 10:21:54.896 -03:00 [INF] [NEW_DUAL] 📊 Parameters - Lucro Alvo: 2.00, Alfa: 0.50, Lucro Base: 0.04, R(y/x): 1.70
2025-09-14 10:21:54.896 -03:00 [INF] [NEW_DUAL] 💰 Perdas Acumuladas: 0.00
2025-09-14 10:21:54.897 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.01
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:21:54.902 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:21:55.101 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=947e88e5-02dc-fc2f-ec2e-49e5c040cfe5, AskPrice=0.35, Payout=0.66
2025-09-14 10:21:55.101 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 10:21:55.297 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=f3bf6ae7-18fc-e31a-512d-da67d3c36a20, AskPrice=0.60, Payout=1.16
2025-09-14 10:21:55.297 -03:00 [INF] [NEW_DUAL] Payouts - Contrato 1: 0.66, Contrato 2: 1.16
2025-09-14 10:21:55.297 -03:00 [INF] [NEW_DUAL] Final stakes - Contrato 1: 0.60, Contrato 2: 0.35
2025-09-14 10:21:55.298 -03:00 [INF] [DUAL] Primeira entrada - Random choice: Contrato principal MENOR stake
2025-09-14 10:21:55.298 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0.35, Lower: 0.60
2025-09-14 10:21:55.298 -03:00 [INF] [DUAL] Stake MAIOR (0.60) vai para: Lower
2025-09-14 10:21:55.298 -03:00 [INF] [DUAL] Stakes finais - Higher: 0.35, Lower: 0.60
2025-09-14 10:21:55.298 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 10:21:55.512 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=947e88e5-02dc-fc2f-ec2e-49e5c040cfe5, AskPrice=0.35, Payout=0.66
2025-09-14 10:21:55.513 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 10:21:55.724 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=f3bf6ae7-18fc-e31a-512d-da67d3c36a20, AskPrice=0.60, Payout=1.16
2025-09-14 10:21:55.724 -03:00 [INF] [DUAL] Payouts - Contrato 1 (Higher): 0.66, Contrato 2 (Lower): 1.16
2025-09-14 10:21:55.724 -03:00 [INF] [DUAL ENTRY] ===== INICIANDO COMPRAS DUAIS =====
2025-09-14 10:21:55.724 -03:00 [INF] [DUAL ENTRY] Saldo antes das compras: 17714.20
2025-09-14 10:21:55.724 -03:00 [INF] [DUAL ENTRY] Active Exposure antes: 0.00
2025-09-14 10:21:55.724 -03:00 [INF] [DUAL ENTRY] Total stake a ser debitado: 0.95
2025-09-14 10:21:55.724 -03:00 [INF] [DUAL] INTENT BUY -> C1=Higher, Stake=0.35, ProposalId=947e88e5-02dc-fc2f-ec2e-49e5c040cfe5
2025-09-14 10:21:55.724 -03:00 [INF] [DUAL] INTENT BUY -> C2=Lower, Stake=0.60, ProposalId=f3bf6ae7-18fc-e31a-512d-da67d3c36a20
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17714.20
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.20
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.85
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.85
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.20
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.35 (0.00%)
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:21:56.000 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:21:56.009 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.85
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.20
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.25
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.25
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.20
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.95 (0.01%)
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:21:56.010 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:21:56.024 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:21:56.024 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-14 10:21:56.024 -03:00 [INF] [DUAL ENTRY] Saldo após compras: 17714.20
2025-09-14 10:21:56.024 -03:00 [INF] [DUAL ENTRY] Active Exposure após: 0.00
2025-09-14 10:21:56.024 -03:00 [INF] [DUAL ENTRY] Diferença no saldo: 0.00
2025-09-14 10:21:56.024 -03:00 [INF] [DUAL ENTRY] Diferença no Active Exposure: 0.00
2025-09-14 10:21:56.024 -03:00 [INF] [DUAL ENTRY] Contract 1 ID: ************
2025-09-14 10:21:56.024 -03:00 [INF] [DUAL ENTRY] Contract 2 ID: ************
2025-09-14 10:21:56.029 -03:00 [INF] New maximum stake recorded: 0.35
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Contract Type: Higher
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Stake: 0.35
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Payout: 0.66
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Entries count: 0 -> 1
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Saldo: 17714.20 -> 17713.85
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Active Exposure: 0.00 -> 0.35
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.35
2025-09-14 10:21:56.030 -03:00 [INF] New maximum stake recorded: 0.60
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Contract Type: Lower
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Stake: 0.60
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Payout: 1.16
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Entries count: 1 -> 2
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Saldo: 17713.85 -> 17713.25
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Active Exposure: 0.35 -> 0.95
2025-09-14 10:21:56.030 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.60
2025-09-14 10:21:56.030 -03:00 [INF] [DUAL] Compra dupla executada no nível 0 (máximo: 5)
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.01
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 2
2025-09-14 10:21:56.069 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:21:57.927 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:21:57.929 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:21:57.929 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:21:57.931 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:21:57.931 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:21:57.932 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:21:57.933 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:21:57.933 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:21:59.961 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:21:59.962 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:21:59.962 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:21:59.962 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:21:59.962 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:21:59.962 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:21:59.962 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:21:59.962 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.25
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.25
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.25
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.25
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.25
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:21:59.962 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.25
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.25
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.25
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.25
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.25
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:21:59.993 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.25
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.25
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17714.41
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 1.16
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17714.41
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.25
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.16 (0.01%)
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:22:00.028 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:22:00.028 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:22:00.028 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.16 (0.01%)
2025-09-14 10:22:00.028 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:22:01.896 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:22:01.896 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:22:01.896 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:22:01.898 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.56, Corrected=0.56, Stake=0.60, Payout=1.16
2025-09-14 10:22:01.898 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:22:01.898 - Profit: 0.56, Win: True, Exit: 5991.0730
2025-09-14 10:22:01.898 -03:00 [INF] [DUAL] Contract result received at 10:22:01.898 - WIN: true
2025-09-14 10:22:01.899 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-14 10:22:01.899 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 10:22:01.901 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Contract Type: Lower
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Stake: 0.60
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Payout: 1.16
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Entry Price: 5991.2240
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Exit Price: 5991.0730
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Raw Profit: 0.560000
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Rounded Profit: 0.56
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Result: WIN
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 10:22:01.901 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 10:22:01.901 -03:00 [INF] [BALANCE] Before transaction: 17713.85
2025-09-14 10:22:01.901 -03:00 [INF] Profit Table updated for contract ************: Profit=0.56, ExitPrice=5991.073, ExitTime=13:30:34
2025-09-14 10:22:01.903 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 10:22:01.903 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 10:22:01.903 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 10:22:01.903 -03:00 [INF] [SESSION PROFIT] Profit do contrato: 0.56
2025-09-14 10:22:01.903 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:22:01.903 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:22:01.903 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit 0.56
2025-09-14 10:22:01.903 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: False, Contract2Completed: False
2025-09-14 10:22:01.903 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 10:22:01.903 -03:00 [INF] [DUAL DEBUG] Contract index: 1
2025-09-14 10:22:01.903 -03:00 [INF] [DUAL] Contract 2 finished: Stake=0.60, Profit=0.56
2025-09-14 10:22:01.903 -03:00 [INF] [SESSION PROFIT] Contract 2 - Stake: 0.60, Profit: 0.56
2025-09-14 10:22:01.903 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: False, Contract2Completed: True
2025-09-14 10:22:01.903 -03:00 [INF] [DUAL DEBUG] Waiting for other contract to complete...
2025-09-14 10:22:01.903 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:22:01.903 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:32.000, price=5991.224
2025-09-14 10:22:01.905 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:22:01.905 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:22:01.905 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.35, Corrected=-0.35, Stake=0.35, Payout=0.66
2025-09-14 10:22:01.905 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:22:01.905 - Profit: -0.35, Win: False, Exit: 5991.0730
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL] Contract result received at 10:22:01.905 - WIN: false
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 10:22:01.905 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Contract Type: Higher
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Stake: 0.35
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Payout: 0.66
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Entry Price: 5991.2240
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Exit Price: 5991.0730
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Raw Profit: -0.350000
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Rounded Profit: -0.35
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Result: LOSS
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 10:22:01.905 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 10:22:01.905 -03:00 [INF] [BALANCE] Before transaction: 17714.20
2025-09-14 10:22:01.905 -03:00 [INF] Profit Table updated for contract ************: Profit=-0.35, ExitPrice=5991.073, ExitTime=13:30:34
2025-09-14 10:22:01.905 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 10:22:01.905 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 10:22:01.905 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 10:22:01.905 -03:00 [INF] [SESSION PROFIT] Profit do contrato: -0.35
2025-09-14 10:22:01.905 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:22:01.905 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit -0.35
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: False, Contract2Completed: True
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL DEBUG] Contract index: 0
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL] Contract 1 finished: Stake=0.35, Profit=-0.35
2025-09-14 10:22:01.905 -03:00 [INF] [SESSION PROFIT] Contract 1 - Stake: 0.35, Profit: -0.35
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: True, Contract2Completed: True
2025-09-14 10:22:01.905 -03:00 [INF] [DUAL DEBUG] ✅ Both contracts completed in OnContractFinished - calling ProcessDualLevelComplete
2025-09-14 10:22:01.906 -03:00 [INF] [DUAL DEBUG] 🚀 Executing ProcessDualLevelComplete (FAST ASYNC)...
2025-09-14 10:22:01.908 -03:00 [INF] [DUAL] 🎯 ProcessDualLevelComplete STARTED - Level 0, SessionProfit: 0.00
2025-09-14 10:22:01.908 -03:00 [INF] [DUAL REAL RESULT] Primeira entrada (Level 0) - Contract1=-0.35, Contract2=0.56
2025-09-14 10:22:01.908 -03:00 [INF] [SESSION PROFIT] ===== CALCULANDO SESSION PROFIT =====
2025-09-14 10:22:01.908 -03:00 [INF] [SESSION PROFIT] Contract 1 Profit: -0.35
2025-09-14 10:22:01.908 -03:00 [INF] [SESSION PROFIT] Contract 2 Profit: 0.56
2025-09-14 10:22:01.908 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:22:01.908 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:22:01.908 -03:00 [INF] [SESSION PROFIT] 🎯 Resultado líquido da dupla (pairNet): 0.21
2025-09-14 10:22:01.908 -03:00 [INF] [SESSION PROFIT] 🧮 Cálculo: -0.35 + 0.56 = 0.21
2025-09-14 10:22:01.908 -03:00 [INF] [DUAL_PERDAS] Lucro de 0.21 recuperou 0.00. Perdas restantes: 0.00
2025-09-14 10:22:01.908 -03:00 [INF] [NEW_DUAL] 💰 Perdas acumuladas - Antes: 0.00, Depois: 0.00, Mudança: 0.00
2025-09-14 10:22:01.908 -03:00 [INF] [SESSION PROFIT] Contract 2 ganhou, Contract 1 perdeu. LosingContractTypeIndex = 0
2025-09-14 10:22:01.909 -03:00 [INF] [SESSION PROFIT] SessionProfit atualizado: 0.00 + 0.21 = 0.21
2025-09-14 10:22:01.909 -03:00 [INF] [SESSION PROFIT] Diferença no SessionProfit: 0.21
2025-09-14 10:22:01.909 -03:00 [INF] [DUAL] Pair result: -0.35 + 0.56 = 0.21; SessionProfit (after): 0.21
2025-09-14 10:22:01.909 -03:00 [INF] [DUAL] Updated _previousSessionProfit to: 0.21
2025-09-14 10:22:01.909 -03:00 [INF] [DUAL] SessionProfit calculation completed - avoiding duplicate calculation
2025-09-14 10:22:01.909 -03:00 [INF] [MICRO] Transferência de micro-metas executada. Unit=0.04, TotalProfit=0.20, SessionProfit=0.01
2025-09-14 10:22:01.909 -03:00 [INF] [SESSION_END_DEBUG] Sessão NÃO encerrada - SessionProfit: 0.01, Target: 2.00
2025-09-14 10:22:01.909 -03:00 [INF] [SESSION_END_DEBUG] Diferença para target: 1.99
2025-09-14 10:22:01.909 -03:00 [INF] [SESSION_END_DEBUG] Nível atual: 0/5, Sessão: 1/1
2025-09-14 10:22:01.909 -03:00 [INF] [DUAL] VITÓRIA detectada (resultado líquido: 0.21) - stake maior ganhou
2025-09-14 10:22:01.909 -03:00 [INF] [DUAL] Stakes resetadas para valores iniciais - prejuízo acumulado zerado
2025-09-14 10:22:01.909 -03:00 [INF] [DUAL DEBUG] Continuing within session 1/1 at level 0/5
2025-09-14 10:22:01.909 -03:00 [INF] [DUAL] 🚀 Executing next dual entry FAST PATH... (Level 0/5)
2025-09-14 10:22:01.911 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:22:01.911 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:22:01.911 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 10:22:01.911 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:22:01.911
2025-09-14 10:22:01.911 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:22:01.911
2025-09-14 10:22:01.911 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.224 at 13:30:32
2025-09-14 10:22:02.140 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 228.5247ms às 10:22:02.140
2025-09-14 10:22:02.140 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 228.6997ms às 10:22:02.140
2025-09-14 10:22:02.393 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:02.393 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17714.41
2025-09-14 10:22:02.393 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:02.393 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:02.393 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17714.06
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17714.06
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.34 (0.00%)
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:02.394 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:22:02.402 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:22:02.410 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17714.06
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.46
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.46
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.94 (0.01%)
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:02.410 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:22:03.261 -03:00 [WRN] [DUAL WATCHDOG] Nenhuma compra detectada após conclusão do nível - tentando novamente em 400ms
2025-09-14 10:22:03.662 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:22:03.662 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:22:03.662 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 10:22:03.662 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:22:03.662
2025-09-14 10:22:03.662 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:22:03.662
2025-09-14 10:22:03.947 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:03.949 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:03.951 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:03.951 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:03.979 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 317.059ms às 10:22:03.979
2025-09-14 10:22:03.979 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 317.3335ms às 10:22:03.979
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.46
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.86
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.86
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.54 (0.01%)
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:04.220 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:04.220 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:22:04.220 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.54 (0.01%)
2025-09-14 10:22:04.220 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:22:04.236 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:22:04.236 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.86
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.51
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.51
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.89 (0.01%)
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:04.237 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:04.237 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:22:04.237 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.89 (0.01%)
2025-09-14 10:22:04.237 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:22:05.088 -03:00 [INF] [DUAL DEBUG] ✅ ProcessDualLevelComplete completed
2025-09-14 10:22:05.918 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:05.946 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:05.947 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:05.947 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:05.947 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:05.948 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:06.055 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:06.055 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.51
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.17
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.66
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.17
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.23 (0.01%)
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:06.198 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:06.198 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:22:06.198 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.23 (0.01%)
2025-09-14 10:22:06.198 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.17
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.17
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.17
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.23 (0.01%)
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:06.827 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:06.827 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:22:06.827 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.23 (0.01%)
2025-09-14 10:22:06.827 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:22:07.926 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:07.928 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:07.928 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:07.929 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:22:07.929 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.60, Corrected=-0.60, Stake=0.60, Payout=1.16
2025-09-14 10:22:07.929 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:22:07.929 - Profit: -0.60, Win: False, Exit: 5990.7370
2025-09-14 10:22:07.929 -03:00 [INF] [DUAL] Contract result received at 10:22:07.929 - WIN: false
2025-09-14 10:22:07.929 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:22:07.929 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 0
2025-09-14 10:22:07.929 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:22:07.929 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:22:07.929 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:22:07.929 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:22:07.930 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.78 at 13:30:38
2025-09-14 10:22:07.950 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:38.000, price=5990.78
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.20, MaxLossAmount: 0.01
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:22:07.951 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:22:07.952 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:22:07.952 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.31, Corrected=0.31, Stake=0.35, Payout=0.66
2025-09-14 10:22:07.952 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:22:07.952 - Profit: 0.31, Win: True, Exit: 5990.7370
2025-09-14 10:22:07.952 -03:00 [INF] [DUAL] Contract result received at 10:22:07.952 - WIN: true
2025-09-14 10:22:07.952 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:22:07.952 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 0
2025-09-14 10:22:07.952 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:22:07.952 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:22:07.952 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:22:07.952 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:22:07.952 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.78 at 13:30:38
2025-09-14 10:22:07.952 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.20, MaxLossAmount: 0.01
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:22:07.972 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.17
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.17
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.17
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.23 (0.01%)
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:08.743 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:08.743 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:22:08.743 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.23 (0.01%)
2025-09-14 10:22:08.743 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.17
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.83
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.66
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.83
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:08.843 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:22:09.905 -03:00 [INF] [VOLATILIDADE] Preço: 5990.62000, Média: 5991.01970, Volatilidade: 0.000059
2025-09-14 10:22:09.923 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:09.923 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:22:09.923 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.60, Corrected=-0.60, Stake=0.60, Payout=1.16
2025-09-14 10:22:09.923 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:22:09.923 - Profit: -0.60, Win: False, Exit: 5990.6760
2025-09-14 10:22:09.923 -03:00 [INF] [DUAL] Contract result received at 10:22:09.923 - WIN: false
2025-09-14 10:22:09.924 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:22:09.924 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 0
2025-09-14 10:22:09.924 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:22:09.924 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:22:09.924 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:22:09.924 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:22:09.924 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.737 at 13:30:40
2025-09-14 10:22:09.924 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:30:40.000, price=5990.737
2025-09-14 10:22:09.945 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:22:09.945 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.31, Corrected=0.31, Stake=0.35, Payout=0.66
2025-09-14 10:22:09.945 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:22:09.945 - Profit: 0.31, Win: True, Exit: 5990.6760
2025-09-14 10:22:09.945 -03:00 [INF] [DUAL] Contract result received at 10:22:09.945 - WIN: true
2025-09-14 10:22:09.945 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:22:09.945 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 0
2025-09-14 10:22:09.945 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:22:09.945 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:22:09.945 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:22:09.945 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:22:09.946 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.737 at 13:30:40
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.83
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.83
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.83
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:09.946 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.20, MaxLossAmount: 0.01
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:22:09.967 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:22:11.912 -03:00 [INF] [VOLATILIDADE] Preço: 5990.54600, Média: 5990.97664, Volatilidade: 0.000060
2025-09-14 10:22:13.911 -03:00 [INF] [VOLATILIDADE] Preço: 5990.79300, Média: 5990.96133, Volatilidade: 0.000058
2025-09-14 10:22:15.926 -03:00 [INF] [VOLATILIDADE] Preço: 5990.93100, Média: 5990.95900, Volatilidade: 0.000056
2025-09-14 10:22:17.912 -03:00 [INF] [VOLATILIDADE] Preço: 5990.92700, Média: 5990.95671, Volatilidade: 0.000054
2025-09-14 10:22:19.913 -03:00 [INF] [VOLATILIDADE] Preço: 5990.95200, Média: 5990.95640, Volatilidade: 0.000052
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.83
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.83
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.83
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:19.915 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:22:21.916 -03:00 [INF] [VOLATILIDADE] Preço: 5990.94200, Média: 5990.95550, Volatilidade: 0.000051
2025-09-14 10:22:23.889 -03:00 [INF] [VOLATILIDADE] Preço: 5990.86800, Média: 5990.95035, Volatilidade: 0.000049
2025-09-14 10:22:25.921 -03:00 [INF] [VOLATILIDADE] Preço: 5990.93600, Média: 5990.94956, Volatilidade: 0.000048
2025-09-14 10:22:27.930 -03:00 [INF] [VOLATILIDADE] Preço: 5990.93300, Média: 5990.94868, Volatilidade: 0.000047
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.20, MaxLossAmount: 0.01
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:22:29.506 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:22:29.936 -03:00 [INF] [VOLATILIDADE] Preço: 5991.07000, Média: 5990.95475, Volatilidade: 0.000046
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.83
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.40
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.83
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.83
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.40
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17714.20
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:22:29.970 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.20, MaxLossAmount: 0.01
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:22:30.838 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:22:31.991 -03:00 [INF] [VOLATILIDADE] Preço: 5990.85400, Média: 5990.94995, Volatilidade: 0.000045
2025-09-14 10:22:32.003 -03:00 [INF] Application is shutting down...
2025-09-14 10:29:40.505 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-14 10:29:40.528 -03:00 [INF] Hosting environment: Production
2025-09-14 10:29:40.528 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.5
2025-09-14 10:29:40.775 -03:00 [INF] [CONFIG] Caminho do arquivo de configuração: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:29:40.784 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 10:29:40.791 -03:00 [INF] [AUTO-PAUSE] Timer de pausa automática inicializado com intervalo de 30 segundos
2025-09-14 10:29:40.792 -03:00 [INF] Conectando à API Deriv...
2025-09-14 10:29:41.023 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:29:41.023 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 10:29:41.023 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:29:41.023 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:29:41.023 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:29:41.023 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 10:29:41.023 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 10:29:41.024 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:29:41.024 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 5
2025-09-14 10:29:41.024 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:29:41.024 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: False
2025-09-14 10:29:41.024 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.00
2025-09-14 10:29:41.024 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 10:29:41.024 -03:00 [INF] [DEBUG CanExecuteBuy] AskPrice: 0
2025-09-14 10:29:41.024 -03:00 [INF] [DEBUG CanExecuteBuy] CurrentProposalId: 'null'
2025-09-14 10:29:41.024 -03:00 [INF] [DEBUG CanExecuteBuy] Normal Mode Result: False
2025-09-14 10:29:41.272 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:21:51
2025-09-14 10:29:41.280 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:29:41.280 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:29:41.282 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:29:41.282 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:29:41.284 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:29:41.284 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:29:41.284 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:29:41.284 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:29:41.287 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 10:29:41.287 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:29:41.287 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:29:41.287 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: null
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:29:41.391 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: False (allowing automatic continuation)
2025-09-14 10:29:41.494 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-14 10:29:41.590 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:29:41.591 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:21:51
2025-09-14 10:29:41.653 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:29:41.653 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:29:41.653 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:29:41.653 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:29:41.653 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:29:41.653 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:29:41.935 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:29:41.936 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 0.00
2025-09-14 10:29:41.936 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 0.00
2025-09-14 10:29:41.936 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:29:41.936 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:29:41.936 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:29:41.936 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.83
2025-09-14 10:29:41.936 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 17713.83
2025-09-14 10:29:41.936 -03:00 [INF] [BALANCE] Initial balance set to: 17713.83
2025-09-14 10:29:41.937 -03:00 [INF] Status de conexão alterado para: True
2025-09-14 10:29:41.940 -03:00 [INF] [CONNECTION] Connection reestablished - restoring application state
2025-09-14 10:29:41.943 -03:00 [INF] [RESTORE] Step 1: Force loading latest configuration
2025-09-14 10:29:41.943 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 10:29:41.943 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:29:41
2025-09-14 10:29:41.943 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:29:41.943 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:29:41.943 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:29:41.943 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:29:41.943 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:29:41.943 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:29:41.943 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:29:41.943 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:29:41.943 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 10:29:41.943 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:29:41.943 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:29:41.943 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:29:41.944 -03:00 [INF] [RESTORE] State to restore - Market: Derived, Symbol: R_10, Contract: CALLE, DualContract: PUTE
2025-09-14 10:29:41.944 -03:00 [INF] [RESTORE] Step 2: Loading active symbols
2025-09-14 10:29:41.944 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-14 10:29:42.268 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:29:42.269 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:29:41
2025-09-14 10:29:42.321 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:29:42.321 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:29:42.321 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:29:42.321 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:29:42.321 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:29:42.321 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:29:42.449 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-14 10:29:42.452 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-14 10:29:42.452 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-14 10:29:42.452 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-14 10:29:42.452 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-14 10:29:42.452 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-14 10:29:42.453 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-14 10:29:42.453 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-14 10:29:42.453 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 10:29:42.455 -03:00 [INF] [CONFIG] Mercado restaurado: Derived
2025-09-14 10:29:42.456 -03:00 [INF] [RESTORE] Step 3: Restoring selections with retry
2025-09-14 10:29:42.457 -03:00 [INF] [RESTORE RETRY] Attempt 1/5 - Starting restoration
2025-09-14 10:29:42.458 -03:00 [INF] [RESTORE] Starting selection restoration - Market: Derived, Symbol: R_10, Contract: CALLE, Dual: PUTE
2025-09-14 10:29:42.470 -03:00 [INF] [RESTORE] Markets loaded successfully: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 10:29:42.471 -03:00 [INF] [RESTORE] ✓ Market restored: 'Derived' -> 'Derived'
2025-09-14 10:29:42.472 -03:00 [INF] [RESTORE] ✓ SubMarket restored: 'Continuous Indices' -> 'Continuous Indices'
2025-09-14 10:29:42.477 -03:00 [INF] [RESTORE] ✓ Symbol restored: 'R_10' -> 'R_10'
2025-09-14 10:29:42.478 -03:00 [INF] [RESTORE] Contract types list empty, waiting... attempt 1/25
2025-09-14 10:29:42.780 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:29:42.780 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-14 10:29:42.786 -03:00 [INF] [RESTORE] ✓ Main contract type restored: 'CALLE' (attempt 2)
2025-09-14 10:29:42.786 -03:00 [INF] [RESTORE] ✓ Dual contract type restored: 'PUTE' (attempt 1)
2025-09-14 10:29:42.787 -03:00 [INF] [RESTORE] Final restoration state:
2025-09-14 10:29:42.787 -03:00 [INF] [RESTORE]   Market: 'Derived' -> 'Derived' ✓
2025-09-14 10:29:42.787 -03:00 [INF] [RESTORE]   SubMarket: 'Continuous Indices' -> 'Continuous Indices' ✓
2025-09-14 10:29:42.787 -03:00 [INF] [RESTORE]   Symbol: 'R_10' -> 'R_10' ✓
2025-09-14 10:29:42.787 -03:00 [INF] [RESTORE]   Contract: 'CALLE' -> 'CALLE' ✓
2025-09-14 10:29:42.787 -03:00 [INF] [RESTORE]   DualContract: 'PUTE' -> 'PUTE' ✓
2025-09-14 10:29:42.788 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-14 10:29:43.086 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:29:43.087 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:29:42
2025-09-14 10:29:43.141 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:29:43.141 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:29:43.141 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:29:43.141 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:29:43.141 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:29:43.141 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:29:43.479 -03:00 [INF] [RESTORE VERIFY] Market: True, Symbol: True, Contract: True, DualContract: True
2025-09-14 10:29:43.480 -03:00 [INF] [RESTORE RETRY] ✓ Restoration successful on attempt 1
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:29:47.167 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:29:48.189 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:29:48.189 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:29:48.189 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:29:48.189 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:29:48.189 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:29:48.189 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:29:48.189 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:29:48.189 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:29:48.190 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:29:48.190 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:29:48.190 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:29:48.190 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:29:48.190 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:29:48.190 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:29:48.190 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:29:48.190 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:29:48.190 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:29:48.403 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:29:48.405 -03:00 [INF] [BUY] ExecuteBuyCommand called - IsDualEnabled: True
2025-09-14 10:29:48.405 -03:00 [INF] [BUY] Dual mode detected, calling ExecuteDualEntryCommand
2025-09-14 10:29:48.409 -03:00 [INF] [DUAL DEBUG] 🚀 ExecuteDualEntryCommand started
2025-09-14 10:29:48.409 -03:00 [INF] [DUAL DEBUG] Connection check passed
2025-09-14 10:29:48.409 -03:00 [INF] [DUAL DEBUG] Timing check bypassed for dual mode (no-delay)
2025-09-14 10:29:48.409 -03:00 [INF] [DUAL DEBUG] Validating contract types - ContractType: CALLE, DualContractType: PUTE, Symbol: R_10
2025-09-14 10:29:48.409 -03:00 [INF] [DUAL] Iniciando primeira sessão dual - Session 1
2025-09-14 10:29:48.409 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 0/5, Session 1/1
2025-09-14 10:29:48.410 -03:00 [INF] [NEW_DUAL] 🚀 Chamando CalculateNewDualStakes...
2025-09-14 10:29:48.411 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:29:48.411 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:29:48.411 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 10:29:48.411 -03:00 [INF] [NEW_DUAL] ✅ Calculated stakes using new formulas - X: 0.35, Y: 0.60
2025-09-14 10:29:48.411 -03:00 [INF] [NEW_DUAL] 📊 Parameters - Lucro Alvo: 2.00, Alfa: 0.50, Lucro Base: 0.04, R(y/x): 1.70
2025-09-14 10:29:48.411 -03:00 [INF] [NEW_DUAL] 💰 Perdas Acumuladas: 0.00
2025-09-14 10:29:48.412 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:29:48.422 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:29:48.638 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=ccf41b6d-e324-3292-bff0-0e8dbc5233df, AskPrice=0.35, Payout=0.66
2025-09-14 10:29:48.639 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 10:29:48.893 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=d9405b08-6bc3-e0d4-36af-3b6405599a34, AskPrice=0.60, Payout=1.16
2025-09-14 10:29:48.893 -03:00 [INF] [NEW_DUAL] Payouts - Contrato 1: 0.66, Contrato 2: 1.16
2025-09-14 10:29:48.893 -03:00 [INF] [NEW_DUAL] Final stakes - Contrato 1: 0.60, Contrato 2: 0.35
2025-09-14 10:29:48.893 -03:00 [INF] [DUAL] Primeira entrada - Random choice: Contrato principal MENOR stake
2025-09-14 10:29:48.893 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0.35, Lower: 0.60
2025-09-14 10:29:48.893 -03:00 [INF] [DUAL] Stake MAIOR (0.60) vai para: Lower
2025-09-14 10:29:48.893 -03:00 [INF] [DUAL] Stakes finais - Higher: 0.35, Lower: 0.60
2025-09-14 10:29:48.893 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 10:29:49.098 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=ccf41b6d-e324-3292-bff0-0e8dbc5233df, AskPrice=0.35, Payout=0.66
2025-09-14 10:29:49.098 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 10:29:49.297 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=d9405b08-6bc3-e0d4-36af-3b6405599a34, AskPrice=0.60, Payout=1.16
2025-09-14 10:29:49.297 -03:00 [INF] [DUAL] Payouts - Contrato 1 (Higher): 0.66, Contrato 2 (Lower): 1.16
2025-09-14 10:29:49.297 -03:00 [INF] [DUAL ENTRY] ===== INICIANDO COMPRAS DUAIS =====
2025-09-14 10:29:49.297 -03:00 [INF] [DUAL ENTRY] Saldo antes das compras: 17713.83
2025-09-14 10:29:49.297 -03:00 [INF] [DUAL ENTRY] Active Exposure antes: 0.00
2025-09-14 10:29:49.297 -03:00 [INF] [DUAL ENTRY] Total stake a ser debitado: 0.95
2025-09-14 10:29:49.297 -03:00 [INF] [DUAL] INTENT BUY -> C1=Higher, Stake=0.35, ProposalId=ccf41b6d-e324-3292-bff0-0e8dbc5233df
2025-09-14 10:29:49.297 -03:00 [INF] [DUAL] INTENT BUY -> C2=Lower, Stake=0.60, ProposalId=d9405b08-6bc3-e0d4-36af-3b6405599a34
2025-09-14 10:29:49.539 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:29:49.539 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.83
2025-09-14 10:29:49.539 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.83
2025-09-14 10:29:49.539 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:29:49.539 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:29:49.539 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:29:49.539 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.48
2025-09-14 10:29:49.539 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 10:29:49.540 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:29:49.540 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.48
2025-09-14 10:29:49.540 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.83
2025-09-14 10:29:49.540 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.35 (0.00%)
2025-09-14 10:29:49.540 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.83
2025-09-14 10:29:49.540 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:29:49.540 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:29:49.540 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:29:49.540 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:29:49.556 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.48
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.83
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.88
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.88
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.83
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.95 (0.01%)
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.83
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:29:49.556 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:29:49.557 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:29:49.557 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-14 10:29:49.557 -03:00 [INF] [DUAL ENTRY] Saldo após compras: 17713.83
2025-09-14 10:29:49.557 -03:00 [INF] [DUAL ENTRY] Active Exposure após: 0.00
2025-09-14 10:29:49.557 -03:00 [INF] [DUAL ENTRY] Diferença no saldo: 0.00
2025-09-14 10:29:49.557 -03:00 [INF] [DUAL ENTRY] Diferença no Active Exposure: 0.00
2025-09-14 10:29:49.557 -03:00 [INF] [DUAL ENTRY] Contract 1 ID: ************
2025-09-14 10:29:49.557 -03:00 [INF] [DUAL ENTRY] Contract 2 ID: ************
2025-09-14 10:29:49.562 -03:00 [INF] New maximum stake recorded: 0.35
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Contract Type: Higher
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Stake: 0.35
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Payout: 0.66
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Entries count: 0 -> 1
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Saldo: 17713.83 -> 17713.48
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Active Exposure: 0.00 -> 0.35
2025-09-14 10:29:49.562 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.35
2025-09-14 10:29:49.563 -03:00 [INF] New maximum stake recorded: 0.60
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Contract Type: Lower
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Stake: 0.60
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Payout: 1.16
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Entries count: 1 -> 2
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Saldo: 17713.48 -> 17712.88
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Active Exposure: 0.35 -> 0.95
2025-09-14 10:29:49.563 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.60
2025-09-14 10:29:49.563 -03:00 [INF] [DUAL] Compra dupla executada no nível 0 (máximo: 5)
2025-09-14 10:29:49.603 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 2
2025-09-14 10:29:49.604 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:29:49.952 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:49.953 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:49.955 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:49.957 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:49.957 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:49.958 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:49.971 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:49.971 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:51.921 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:51.922 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:51.922 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:51.922 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:51.946 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:51.946 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:51.946 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:51.947 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.88
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17712.88
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.88
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.88
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17712.88
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.83
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:29:52.140 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.88
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17712.88
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.88
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.88
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17712.88
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.83
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:29:52.144 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:29:52.195 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:29:52.196 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.88
2025-09-14 10:29:52.196 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17712.88
2025-09-14 10:29:52.196 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:29:52.196 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:29:52.196 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:29:52.196 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17714.04
2025-09-14 10:29:52.197 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 1.16
2025-09-14 10:29:52.197 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:29:52.197 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17714.04
2025-09-14 10:29:52.197 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17712.88
2025-09-14 10:29:52.197 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.16 (0.01%)
2025-09-14 10:29:52.197 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.83
2025-09-14 10:29:52.197 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:29:52.197 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:29:52.197 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:29:52.197 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:29:52.197 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.16 (0.01%)
2025-09-14 10:29:52.197 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:29:53.939 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:53.939 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:53.940 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:29:53.940 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.35, Corrected=-0.35, Stake=0.35, Payout=0.66
2025-09-14 10:29:53.941 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:29:53.940 - Profit: -0.35, Win: False, Exit: 5987.5370
2025-09-14 10:29:53.941 -03:00 [INF] [DUAL] Contract result received at 10:29:53.941 - WIN: false
2025-09-14 10:29:53.941 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-14 10:29:53.941 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 10:29:53.943 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Contract Type: Higher
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Stake: 0.35
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Payout: 0.66
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Entry Price: 5987.7620
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Exit Price: 5987.5370
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Raw Profit: -0.350000
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Rounded Profit: -0.35
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Result: LOSS
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 10:29:53.943 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 10:29:53.943 -03:00 [INF] [BALANCE] Before transaction: 17713.23
2025-09-14 10:29:53.943 -03:00 [INF] Profit Table updated for contract ************: Profit=-0.35, ExitPrice=5987.537, ExitTime=13:38:26
2025-09-14 10:29:53.945 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 10:29:53.945 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 10:29:53.945 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 10:29:53.945 -03:00 [INF] [SESSION PROFIT] Profit do contrato: -0.35
2025-09-14 10:29:53.945 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:29:53.945 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:29:53.945 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit -0.35
2025-09-14 10:29:53.945 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: False, Contract2Completed: False
2025-09-14 10:29:53.945 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 10:29:53.945 -03:00 [INF] [DUAL DEBUG] Contract index: 0
2025-09-14 10:29:53.945 -03:00 [INF] [DUAL] Contract 1 finished: Stake=0.35, Profit=-0.35
2025-09-14 10:29:53.945 -03:00 [INF] [SESSION PROFIT] Contract 1 - Stake: 0.35, Profit: -0.35
2025-09-14 10:29:53.945 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: True, Contract2Completed: False
2025-09-14 10:29:53.945 -03:00 [INF] [DUAL DEBUG] Waiting for other contract to complete...
2025-09-14 10:29:53.947 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:53.947 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:38:24.000, price=5987.762
2025-09-14 10:29:53.947 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:53.948 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:29:53.948 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.56, Corrected=0.56, Stake=0.60, Payout=1.16
2025-09-14 10:29:53.948 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:29:53.948 - Profit: 0.56, Win: True, Exit: 5987.5370
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL] Contract result received at 10:29:53.948 - WIN: true
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 10:29:53.948 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Contract Type: Lower
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Stake: 0.60
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Payout: 1.16
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Entry Price: 5987.7620
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Exit Price: 5987.5370
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Raw Profit: 0.560000
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Rounded Profit: 0.56
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Result: WIN
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 10:29:53.948 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 10:29:53.948 -03:00 [INF] [BALANCE] Before transaction: 17713.83
2025-09-14 10:29:53.948 -03:00 [INF] Profit Table updated for contract ************: Profit=0.56, ExitPrice=5987.537, ExitTime=13:38:26
2025-09-14 10:29:53.948 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 10:29:53.948 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 10:29:53.948 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 10:29:53.948 -03:00 [INF] [SESSION PROFIT] Profit do contrato: 0.56
2025-09-14 10:29:53.948 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:29:53.948 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit 0.56
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: True, Contract2Completed: False
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL DEBUG] Contract index: 1
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL] Contract 2 finished: Stake=0.60, Profit=0.56
2025-09-14 10:29:53.948 -03:00 [INF] [SESSION PROFIT] Contract 2 - Stake: 0.60, Profit: 0.56
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: True, Contract2Completed: True
2025-09-14 10:29:53.948 -03:00 [INF] [DUAL DEBUG] ✅ Both contracts completed in OnContractFinished - calling ProcessDualLevelComplete
2025-09-14 10:29:53.949 -03:00 [INF] [DUAL DEBUG] 🚀 Executing ProcessDualLevelComplete (FAST ASYNC)...
2025-09-14 10:29:53.951 -03:00 [INF] [DUAL] 🎯 ProcessDualLevelComplete STARTED - Level 0, SessionProfit: 0.00
2025-09-14 10:29:53.951 -03:00 [INF] [DUAL REAL RESULT] Primeira entrada (Level 0) - Contract1=-0.35, Contract2=0.56
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] ===== CALCULANDO SESSION PROFIT =====
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] Contract 1 Profit: -0.35
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] Contract 2 Profit: 0.56
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] 🎯 Resultado líquido da dupla (pairNet): 0.21
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] 🧮 Cálculo: -0.35 + 0.56 = 0.21
2025-09-14 10:29:53.951 -03:00 [INF] [DUAL_PERDAS] Lucro de 0.21 recuperou 0.00. Perdas restantes: 0.00
2025-09-14 10:29:53.951 -03:00 [INF] [NEW_DUAL] 💰 Perdas acumuladas - Antes: 0.00, Depois: 0.00, Mudança: 0.00
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] Contract 2 ganhou, Contract 1 perdeu. LosingContractTypeIndex = 0
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] SessionProfit atualizado: 0.00 + 0.21 = 0.21
2025-09-14 10:29:53.951 -03:00 [INF] [SESSION PROFIT] Diferença no SessionProfit: 0.21
2025-09-14 10:29:53.951 -03:00 [INF] [DUAL] Pair result: -0.35 + 0.56 = 0.21; SessionProfit (after): 0.21
2025-09-14 10:29:53.951 -03:00 [INF] [DUAL] Updated _previousSessionProfit to: 0.21
2025-09-14 10:29:53.951 -03:00 [INF] [DUAL] SessionProfit calculation completed - avoiding duplicate calculation
2025-09-14 10:29:53.952 -03:00 [INF] [MICRO] Transferência de micro-metas executada. Unit=0.04, TotalProfit=0.20, SessionProfit=0.01
2025-09-14 10:29:53.952 -03:00 [INF] [SESSION_END_DEBUG] Sessão NÃO encerrada - SessionProfit: 0.01, Target: 2.00
2025-09-14 10:29:53.952 -03:00 [INF] [SESSION_END_DEBUG] Diferença para target: 1.99
2025-09-14 10:29:53.952 -03:00 [INF] [SESSION_END_DEBUG] Nível atual: 0/5, Sessão: 1/1
2025-09-14 10:29:53.952 -03:00 [INF] [DUAL] VITÓRIA detectada (resultado líquido: 0.21) - stake maior ganhou
2025-09-14 10:29:53.952 -03:00 [INF] [DUAL] Stakes resetadas para valores iniciais - prejuízo acumulado zerado
2025-09-14 10:29:53.952 -03:00 [INF] [DUAL DEBUG] Continuing within session 1/1 at level 0/5
2025-09-14 10:29:53.952 -03:00 [INF] [DUAL] 🚀 Executing next dual entry FAST PATH... (Level 0/5)
2025-09-14 10:29:53.953 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:29:53.953 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:29:53.953 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 10:29:53.954 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:29:53.954
2025-09-14 10:29:53.954 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:29:53.954
2025-09-14 10:29:53.954 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5987.762 at 13:38:24
2025-09-14 10:29:54.161 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 206.856ms às 10:29:54.161
2025-09-14 10:29:54.161 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 207.6169ms às 10:29:54.161
2025-09-14 10:29:54.483 -03:00 [INF] [DEBUG] Contrato comprado: 294149773148, subscrevendo para atualizações
2025-09-14 10:29:54.483 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:29:54.483 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17714.04
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.03
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.44
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.44
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.03
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.59 (0.00%)
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.83
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:29:54.484 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.44
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.03
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.09
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.09
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.03
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.94 (0.01%)
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.83
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:29:54.507 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:29:54.508 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:38:58.264 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-14 10:38:58.295 -03:00 [INF] Hosting environment: Production
2025-09-14 10:38:58.295 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.5
2025-09-14 10:38:58.628 -03:00 [INF] [CONFIG] Caminho do arquivo de configuração: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:38:58.642 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 10:38:58.654 -03:00 [INF] [AUTO-PAUSE] Timer de pausa automática inicializado com intervalo de 30 segundos
2025-09-14 10:38:58.655 -03:00 [INF] Conectando à API Deriv...
2025-09-14 10:38:58.971 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:38:58.971 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 5
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: False
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.00
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] AskPrice: 0
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] CurrentProposalId: 'null'
2025-09-14 10:38:58.972 -03:00 [INF] [DEBUG CanExecuteBuy] Normal Mode Result: False
2025-09-14 10:38:59.337 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-14 10:38:59.405 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:29:43
2025-09-14 10:38:59.415 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:38:59.415 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:38:59.417 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:38:59.417 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:38:59.420 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:38:59.420 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:38:59.420 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:38:59.420 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:38:59.423 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 10:38:59.423 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:38:59.423 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:38:59.423 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: null
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:38:59.553 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: False (allowing automatic continuation)
2025-09-14 10:38:59.725 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:38:59.726 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:29:43
2025-09-14 10:38:59.774 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:38:59.774 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 0.00
2025-09-14 10:38:59.774 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 0.00
2025-09-14 10:38:59.774 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:38:59.774 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:38:59.774 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:38:59.775 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:38:59.775 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 17713.96
2025-09-14 10:38:59.775 -03:00 [INF] [BALANCE] Initial balance set to: 17713.96
2025-09-14 10:38:59.776 -03:00 [INF] Status de conexão alterado para: True
2025-09-14 10:38:59.778 -03:00 [INF] [CONNECTION] Connection reestablished - restoring application state
2025-09-14 10:38:59.779 -03:00 [INF] [RESTORE] Step 1: Force loading latest configuration
2025-09-14 10:38:59.779 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:38:59
2025-09-14 10:38:59.788 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:38:59.788 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:38:59.788 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:38:59.788 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:38:59.788 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:38:59.788 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:38:59.788 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:38:59.788 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:38:59.788 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:38:59.788 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:38:59.789 -03:00 [INF] [RESTORE] State to restore - Market: Derived, Symbol: R_10, Contract: CALLE, DualContract: PUTE
2025-09-14 10:38:59.789 -03:00 [INF] [RESTORE] Step 2: Loading active symbols
2025-09-14 10:38:59.789 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-14 10:39:00.095 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:39:00.096 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:38:59
2025-09-14 10:39:00.148 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:39:00.149 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:39:00.149 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:39:00.149 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:39:00.149 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:39:00.149 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:39:00.272 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-14 10:39:00.273 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-14 10:39:00.273 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-14 10:39:00.273 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-14 10:39:00.273 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-14 10:39:00.273 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-14 10:39:00.273 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-14 10:39:00.274 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-14 10:39:00.274 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 10:39:00.277 -03:00 [INF] [CONFIG] Mercado restaurado: Derived
2025-09-14 10:39:00.277 -03:00 [INF] [RESTORE] Step 3: Restoring selections with retry
2025-09-14 10:39:00.278 -03:00 [INF] [RESTORE RETRY] Attempt 1/5 - Starting restoration
2025-09-14 10:39:00.278 -03:00 [INF] [RESTORE] Starting selection restoration - Market: Derived, Symbol: R_10, Contract: CALLE, Dual: PUTE
2025-09-14 10:39:00.289 -03:00 [INF] [RESTORE] Markets loaded successfully: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 10:39:00.291 -03:00 [INF] [RESTORE] ✓ Market restored: 'Derived' -> 'Derived'
2025-09-14 10:39:00.292 -03:00 [INF] [RESTORE] ✓ SubMarket restored: 'Continuous Indices' -> 'Continuous Indices'
2025-09-14 10:39:00.297 -03:00 [INF] [RESTORE] ✓ Symbol restored: 'R_10' -> 'R_10'
2025-09-14 10:39:00.298 -03:00 [INF] [RESTORE] Contract types list empty, waiting... attempt 1/25
2025-09-14 10:39:00.596 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:39:00.597 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:39:00
2025-09-14 10:39:00.599 -03:00 [INF] [RESTORE] Contract types list empty, waiting... attempt 2/25
2025-09-14 10:39:00.649 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:39:00.649 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:39:00.649 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:39:00.649 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:39:00.649 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:39:00.649 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:39:00.901 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:39:00.901 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-14 10:39:00.907 -03:00 [INF] [RESTORE] ✓ Main contract type restored: 'CALLE' (attempt 3)
2025-09-14 10:39:00.908 -03:00 [INF] [RESTORE] ✓ Dual contract type restored: 'PUTE' (attempt 1)
2025-09-14 10:39:00.908 -03:00 [INF] [RESTORE] Final restoration state:
2025-09-14 10:39:00.908 -03:00 [INF] [RESTORE]   Market: 'Derived' -> 'Derived' ✓
2025-09-14 10:39:00.908 -03:00 [INF] [RESTORE]   SubMarket: 'Continuous Indices' -> 'Continuous Indices' ✓
2025-09-14 10:39:00.908 -03:00 [INF] [RESTORE]   Symbol: 'R_10' -> 'R_10' ✓
2025-09-14 10:39:00.908 -03:00 [INF] [RESTORE]   Contract: 'CALLE' -> 'CALLE' ✓
2025-09-14 10:39:00.908 -03:00 [INF] [RESTORE]   DualContract: 'PUTE' -> 'PUTE' ✓
2025-09-14 10:39:00.909 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-14 10:39:01.207 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:39:01.210 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:39:00
2025-09-14 10:39:01.261 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:39:01.261 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:39:01.261 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:39:01.261 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:39:01.262 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:39:01.262 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:39:01.300 -03:00 [INF] [RESTORE VERIFY] Market: True, Symbol: True, Contract: True, DualContract: True
2025-09-14 10:39:01.300 -03:00 [INF] [RESTORE RETRY] ✓ Restoration successful on attempt 1
2025-09-14 10:39:09.997 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:39:09.997 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:39:09.997 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:39:09.997 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:39:09.997 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:39:09.997 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:39:09.997 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:39:09.997 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:39:09.998 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:39:09.998 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:39:09.998 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:39:09.998 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:39:09.998 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:39:09.998 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:39:09.998 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:39:09.998 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:39:09.998 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:39:19.839 -03:00 [INF] [VOLATILIDADE] Preço: 5993.23200, Média: 5993.41510, Volatilidade: 0.000016
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:39:20.058 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:39:20.059 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:39:20.059 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:39:21.916 -03:00 [INF] [VOLATILIDADE] Preço: 5993.46700, Média: 5993.41982, Volatilidade: 0.000015
2025-09-14 10:39:23.854 -03:00 [INF] [VOLATILIDADE] Preço: 5993.54200, Média: 5993.43000, Volatilidade: 0.000016
2025-09-14 10:39:25.866 -03:00 [INF] [VOLATILIDADE] Preço: 5993.49100, Média: 5993.43469, Volatilidade: 0.000015
2025-09-14 10:39:27.883 -03:00 [INF] [VOLATILIDADE] Preço: 5993.43000, Média: 5993.43436, Volatilidade: 0.000015
2025-09-14 10:39:29.854 -03:00 [INF] [VOLATILIDADE] Preço: 5993.34700, Média: 5993.42853, Volatilidade: 0.000015
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:39:29.980 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:39:31.845 -03:00 [INF] [VOLATILIDADE] Preço: 5993.39300, Média: 5993.42631, Volatilidade: 0.000014
2025-09-14 10:39:33.863 -03:00 [INF] [VOLATILIDADE] Preço: 5993.48600, Média: 5993.42982, Volatilidade: 0.000014
2025-09-14 10:39:35.844 -03:00 [INF] [VOLATILIDADE] Preço: 5993.35700, Média: 5993.42578, Volatilidade: 0.000014
2025-09-14 10:39:37.856 -03:00 [INF] [VOLATILIDADE] Preço: 5993.29600, Média: 5993.41895, Volatilidade: 0.000014
2025-09-14 10:39:39.839 -03:00 [INF] [VOLATILIDADE] Preço: 5992.99500, Média: 5993.39775, Volatilidade: 0.000021
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:39:39.965 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:39:41.857 -03:00 [INF] [VOLATILIDADE] Preço: 5993.07400, Média: 5993.38233, Volatilidade: 0.000023
2025-09-14 10:39:43.839 -03:00 [INF] [VOLATILIDADE] Preço: 5992.91200, Média: 5993.36095, Volatilidade: 0.000028
2025-09-14 10:39:45.824 -03:00 [INF] [VOLATILIDADE] Preço: 5992.74200, Média: 5993.33404, Volatilidade: 0.000035
2025-09-14 10:39:47.840 -03:00 [INF] [VOLATILIDADE] Preço: 5992.85300, Média: 5993.31400, Volatilidade: 0.000037
2025-09-14 10:39:49.842 -03:00 [INF] [VOLATILIDADE] Preço: 5992.67700, Média: 5993.28852, Volatilidade: 0.000042
2025-09-14 10:39:50.000 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:39:50.000 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:39:50.001 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:39:51.858 -03:00 [INF] [VOLATILIDADE] Preço: 5992.78200, Média: 5993.26904, Volatilidade: 0.000044
2025-09-14 10:39:53.864 -03:00 [INF] [VOLATILIDADE] Preço: 5992.54700, Média: 5993.24230, Volatilidade: 0.000049
2025-09-14 10:39:55.836 -03:00 [INF] [VOLATILIDADE] Preço: 5992.44300, Média: 5993.21375, Volatilidade: 0.000054
2025-09-14 10:39:57.839 -03:00 [INF] [VOLATILIDADE] Preço: 5992.40500, Média: 5993.18586, Volatilidade: 0.000059
2025-09-14 10:39:59.834 -03:00 [INF] [VOLATILIDADE] Preço: 5992.65500, Média: 5993.16817, Volatilidade: 0.000060
2025-09-14 10:39:59.979 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:39:59.979 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:39:59.979 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:39:59.979 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:39:59.979 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:39:59.979 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:39:59.979 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:39:59.979 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:39:59.979 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:39:59.980 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:39:59.980 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:39:59.980 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:39:59.980 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:39:59.980 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:39:59.980 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:39:59.980 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:39:59.980 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:01.833 -03:00 [INF] [VOLATILIDADE] Preço: 5992.70500, Média: 5993.15323, Volatilidade: 0.000061
2025-09-14 10:40:03.832 -03:00 [INF] [VOLATILIDADE] Preço: 5992.95700, Média: 5993.14709, Volatilidade: 0.000060
2025-09-14 10:40:05.833 -03:00 [INF] [VOLATILIDADE] Preço: 5993.02400, Média: 5993.14336, Volatilidade: 0.000059
2025-09-14 10:40:07.841 -03:00 [INF] [VOLATILIDADE] Preço: 5993.11200, Média: 5993.14244, Volatilidade: 0.000058
2025-09-14 10:40:09.834 -03:00 [INF] [VOLATILIDADE] Preço: 5993.34500, Média: 5993.14823, Volatilidade: 0.000058
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:10.012 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:11.838 -03:00 [INF] [VOLATILIDADE] Preço: 5993.16000, Média: 5993.14856, Volatilidade: 0.000057
2025-09-14 10:40:13.859 -03:00 [INF] [VOLATILIDADE] Preço: 5992.98300, Média: 5993.14408, Volatilidade: 0.000056
2025-09-14 10:40:15.906 -03:00 [INF] [VOLATILIDADE] Preço: 5992.85100, Média: 5993.13637, Volatilidade: 0.000056
2025-09-14 10:40:17.861 -03:00 [INF] [VOLATILIDADE] Preço: 5993.04000, Média: 5993.13390, Volatilidade: 0.000055
2025-09-14 10:40:19.844 -03:00 [INF] [VOLATILIDADE] Preço: 5993.01200, Média: 5993.13085, Volatilidade: 0.000055
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:20.012 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:21.879 -03:00 [INF] [VOLATILIDADE] Preço: 5993.13000, Média: 5993.13083, Volatilidade: 0.000054
2025-09-14 10:40:23.844 -03:00 [INF] [VOLATILIDADE] Preço: 5993.15100, Média: 5993.13131, Volatilidade: 0.000053
2025-09-14 10:40:25.864 -03:00 [INF] [VOLATILIDADE] Preço: 5993.07600, Média: 5993.13002, Volatilidade: 0.000053
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:26.257 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:27.869 -03:00 [INF] [VOLATILIDADE] Preço: 5993.03300, Média: 5993.12782, Volatilidade: 0.000052
2025-09-14 10:40:29.835 -03:00 [INF] [VOLATILIDADE] Preço: 5992.88300, Média: 5993.12238, Volatilidade: 0.000052
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:29.995 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:30.592 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:31.868 -03:00 [INF] [VOLATILIDADE] Preço: 5992.89000, Média: 5993.11733, Volatilidade: 0.000052
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:31.981 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:33.845 -03:00 [INF] [VOLATILIDADE] Preço: 5992.80700, Média: 5993.11072, Volatilidade: 0.000052
2025-09-14 10:40:35.831 -03:00 [INF] [VOLATILIDADE] Preço: 5992.71200, Média: 5993.10242, Volatilidade: 0.000052
2025-09-14 10:40:37.852 -03:00 [INF] [VOLATILIDADE] Preço: 5992.61400, Média: 5993.09245, Volatilidade: 0.000053
2025-09-14 10:40:39.847 -03:00 [INF] [VOLATILIDADE] Preço: 5992.68300, Média: 5993.08426, Volatilidade: 0.000053
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.96
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.96
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:40:39.982 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:39.983 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:40.510 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:41.876 -03:00 [INF] [VOLATILIDADE] Preço: 5992.58000, Média: 5993.06896, Volatilidade: 0.000054
2025-09-14 10:40:42.073 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:42.073 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:42.073 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:42.073 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:42.073 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:42.074 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:42.282 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:42.283 -03:00 [INF] [BUY] ExecuteBuyCommand called - IsDualEnabled: True
2025-09-14 10:40:42.283 -03:00 [INF] [BUY] Dual mode detected, calling ExecuteDualEntryCommand
2025-09-14 10:40:42.286 -03:00 [INF] [DUAL DEBUG] 🚀 ExecuteDualEntryCommand started
2025-09-14 10:40:42.287 -03:00 [INF] [DUAL DEBUG] Connection check passed
2025-09-14 10:40:42.287 -03:00 [INF] [DUAL DEBUG] Timing check bypassed for dual mode (no-delay)
2025-09-14 10:40:42.287 -03:00 [INF] [DUAL DEBUG] Validating contract types - ContractType: CALLE, DualContractType: PUTE, Symbol: R_10
2025-09-14 10:40:42.287 -03:00 [INF] [DUAL] Iniciando primeira sessão dual - Session 1
2025-09-14 10:40:42.287 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 0/5, Session 1/1
2025-09-14 10:40:42.288 -03:00 [INF] [NEW_DUAL] 🚀 Chamando CalculateNewDualStakes...
2025-09-14 10:40:42.288 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:40:42.289 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:40:42.289 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 10:40:42.289 -03:00 [INF] [NEW_DUAL] ✅ Calculated stakes using new formulas - X: 0.35, Y: 0.60
2025-09-14 10:40:42.289 -03:00 [INF] [NEW_DUAL] 📊 Parameters - Lucro Alvo: 2.00, Alfa: 0.50, Lucro Base: 0.04, R(y/x): 1.70
2025-09-14 10:40:42.289 -03:00 [INF] [NEW_DUAL] 💰 Perdas Acumuladas: 0.00
2025-09-14 10:40:42.289 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:42.296 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:42.507 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=9bf39d8a-ad63-360d-b854-8fe7a6770d8a, AskPrice=0.35, Payout=0.66
2025-09-14 10:40:42.507 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 10:40:42.704 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=840556be-5f81-ce35-a8c8-0140cfbccffd, AskPrice=0.60, Payout=1.16
2025-09-14 10:40:42.704 -03:00 [INF] [NEW_DUAL] Payouts - Contrato 1: 0.66, Contrato 2: 1.16
2025-09-14 10:40:42.704 -03:00 [INF] [NEW_DUAL] Final stakes - Contrato 1: 0.60, Contrato 2: 0.35
2025-09-14 10:40:42.704 -03:00 [INF] [DUAL] Primeira entrada - Random choice: Contrato principal MENOR stake
2025-09-14 10:40:42.704 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0.35, Lower: 0.60
2025-09-14 10:40:42.704 -03:00 [INF] [DUAL] Stake MAIOR (0.60) vai para: Lower
2025-09-14 10:40:42.704 -03:00 [INF] [DUAL] Stakes finais - Higher: 0.35, Lower: 0.60
2025-09-14 10:40:42.704 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 10:40:42.928 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=9bf39d8a-ad63-360d-b854-8fe7a6770d8a, AskPrice=0.35, Payout=0.66
2025-09-14 10:40:42.929 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 10:40:43.135 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=840556be-5f81-ce35-a8c8-0140cfbccffd, AskPrice=0.60, Payout=1.16
2025-09-14 10:40:43.135 -03:00 [INF] [DUAL] Payouts - Contrato 1 (Higher): 0.66, Contrato 2 (Lower): 1.16
2025-09-14 10:40:43.135 -03:00 [INF] [DUAL ENTRY] ===== INICIANDO COMPRAS DUAIS =====
2025-09-14 10:40:43.135 -03:00 [INF] [DUAL ENTRY] Saldo antes das compras: 17713.96
2025-09-14 10:40:43.135 -03:00 [INF] [DUAL ENTRY] Active Exposure antes: 0.00
2025-09-14 10:40:43.135 -03:00 [INF] [DUAL ENTRY] Total stake a ser debitado: 0.95
2025-09-14 10:40:43.135 -03:00 [INF] [DUAL] INTENT BUY -> C1=Higher, Stake=0.35, ProposalId=9bf39d8a-ad63-360d-b854-8fe7a6770d8a
2025-09-14 10:40:43.135 -03:00 [INF] [DUAL] INTENT BUY -> C2=Lower, Stake=0.60, ProposalId=840556be-5f81-ce35-a8c8-0140cfbccffd
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.96
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.61
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.61
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.35 (0.00%)
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:43.379 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.61
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.96
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.01
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.01
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.96
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.95 (0.01%)
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:43.389 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:43.396 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:40:43.399 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:40:43.399 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-14 10:40:43.399 -03:00 [INF] [DUAL ENTRY] Saldo após compras: 17713.96
2025-09-14 10:40:43.399 -03:00 [INF] [DUAL ENTRY] Active Exposure após: 0.00
2025-09-14 10:40:43.399 -03:00 [INF] [DUAL ENTRY] Diferença no saldo: 0.00
2025-09-14 10:40:43.399 -03:00 [INF] [DUAL ENTRY] Diferença no Active Exposure: 0.00
2025-09-14 10:40:43.399 -03:00 [INF] [DUAL ENTRY] Contract 1 ID: ************
2025-09-14 10:40:43.399 -03:00 [INF] [DUAL ENTRY] Contract 2 ID: ************
2025-09-14 10:40:43.412 -03:00 [INF] New maximum stake recorded: 0.35
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Contract Type: Higher
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Stake: 0.35
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Payout: 0.66
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Entries count: 0 -> 1
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Saldo: 17713.96 -> 17713.61
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Active Exposure: 0.00 -> 0.35
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.35
2025-09-14 10:40:43.413 -03:00 [INF] New maximum stake recorded: 0.60
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Contract Type: Lower
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Stake: 0.60
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Payout: 1.16
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Entries count: 1 -> 2
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Saldo: 17713.61 -> 17713.01
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Active Exposure: 0.35 -> 0.95
2025-09-14 10:40:43.413 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.60
2025-09-14 10:40:43.413 -03:00 [INF] [DUAL] Compra dupla executada no nível 0 (máximo: 5)
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 2
2025-09-14 10:40:43.455 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:43.855 -03:00 [INF] [VOLATILIDADE] Preço: 5992.62600, Média: 5993.05182, Volatilidade: 0.000054
2025-09-14 10:40:43.920 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:43.921 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:43.923 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:43.924 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:43.929 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:43.929 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:43.941 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:43.942 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:45.837 -03:00 [INF] [VOLATILIDADE] Preço: 5992.51600, Média: 5993.03164, Volatilidade: 0.000054
2025-09-14 10:40:45.920 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:45.920 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:45.921 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:45.921 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:45.921 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:45.921 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:45.921 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:45.921 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:46.653 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.01
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.01
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.01
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.01
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.01
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:40:46.654 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:46.945 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:46.945 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.01
2025-09-14 10:40:46.945 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.01
2025-09-14 10:40:46.945 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:40:46.945 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:40:46.945 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17714.17
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 1.16
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17714.17
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.01
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.16 (0.01%)
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:40:46.946 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:40:46.946 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:40:46.946 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.16 (0.01%)
2025-09-14 10:40:46.946 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:40:47.857 -03:00 [INF] [VOLATILIDADE] Preço: 5992.40300, Média: 5993.01062, Volatilidade: 0.000055
2025-09-14 10:40:47.940 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:47.940 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:47.940 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:40:47.941 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.56, Corrected=0.56, Stake=0.60, Payout=1.16
2025-09-14 10:40:47.941 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:40:47.941 - Profit: 0.56, Win: True, Exit: 5992.5160
2025-09-14 10:40:47.942 -03:00 [INF] [DUAL] Contract result received at 10:40:47.942 - WIN: true
2025-09-14 10:40:47.942 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-14 10:40:47.942 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 10:40:47.944 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 10:40:47.944 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Contract Type: Lower
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Stake: 0.60
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Payout: 1.16
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Entry Price: 5992.6260
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Exit Price: 5992.5160
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Raw Profit: 0.560000
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Rounded Profit: 0.56
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Result: WIN
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 10:40:47.945 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 10:40:47.945 -03:00 [INF] [BALANCE] Before transaction: 17713.61
2025-09-14 10:40:47.945 -03:00 [INF] Profit Table updated for contract ************: Profit=0.56, ExitPrice=5992.516, ExitTime=13:49:21
2025-09-14 10:40:47.947 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 10:40:47.947 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 10:40:47.947 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 10:40:47.947 -03:00 [INF] [SESSION PROFIT] Profit do contrato: 0.56
2025-09-14 10:40:47.947 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:40:47.947 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:40:47.947 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit 0.56
2025-09-14 10:40:47.947 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: False, Contract2Completed: False
2025-09-14 10:40:47.947 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 10:40:47.947 -03:00 [INF] [DUAL DEBUG] Contract index: 1
2025-09-14 10:40:47.947 -03:00 [INF] [DUAL] Contract 2 finished: Stake=0.60, Profit=0.56
2025-09-14 10:40:47.947 -03:00 [INF] [SESSION PROFIT] Contract 2 - Stake: 0.60, Profit: 0.56
2025-09-14 10:40:47.947 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: False, Contract2Completed: True
2025-09-14 10:40:47.947 -03:00 [INF] [DUAL DEBUG] Waiting for other contract to complete...
2025-09-14 10:40:47.949 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:47.949 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:18.000, price=5992.626
2025-09-14 10:40:47.949 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:47.949 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:40:47.949 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.35, Corrected=-0.35, Stake=0.35, Payout=0.66
2025-09-14 10:40:47.949 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:40:47.949 - Profit: -0.35, Win: False, Exit: 5992.5160
2025-09-14 10:40:47.949 -03:00 [INF] [DUAL] Contract result received at 10:40:47.949 - WIN: false
2025-09-14 10:40:47.949 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-14 10:40:47.949 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 10:40:47.950 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Contract Type: Higher
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Stake: 0.35
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Payout: 0.66
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Entry Price: 5992.6260
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Exit Price: 5992.5160
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Raw Profit: -0.350000
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Rounded Profit: -0.35
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Result: LOSS
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 10:40:47.950 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 10:40:47.950 -03:00 [INF] [BALANCE] Before transaction: 17713.96
2025-09-14 10:40:47.950 -03:00 [INF] Profit Table updated for contract ************: Profit=-0.35, ExitPrice=5992.516, ExitTime=13:49:20
2025-09-14 10:40:47.950 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 10:40:47.950 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 10:40:47.950 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 10:40:47.950 -03:00 [INF] [SESSION PROFIT] Profit do contrato: -0.35
2025-09-14 10:40:47.950 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:40:47.950 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:40:47.950 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit -0.35
2025-09-14 10:40:47.950 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: False, Contract2Completed: True
2025-09-14 10:40:47.950 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 10:40:47.950 -03:00 [INF] [DUAL DEBUG] Contract index: 0
2025-09-14 10:40:47.950 -03:00 [INF] [DUAL] Contract 1 finished: Stake=0.35, Profit=-0.35
2025-09-14 10:40:47.950 -03:00 [INF] [SESSION PROFIT] Contract 1 - Stake: 0.35, Profit: -0.35
2025-09-14 10:40:47.950 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: True, Contract2Completed: True
2025-09-14 10:40:47.950 -03:00 [INF] [DUAL DEBUG] ✅ Both contracts completed in OnContractFinished - calling ProcessDualLevelComplete
2025-09-14 10:40:47.950 -03:00 [INF] [DUAL DEBUG] 🚀 Executing ProcessDualLevelComplete (FAST ASYNC)...
2025-09-14 10:40:47.952 -03:00 [INF] [DUAL] 🎯 ProcessDualLevelComplete STARTED - Level 0, SessionProfit: 0.00
2025-09-14 10:40:47.952 -03:00 [INF] [DUAL REAL RESULT] Primeira entrada (Level 0) - Contract1=-0.35, Contract2=0.56
2025-09-14 10:40:47.952 -03:00 [INF] [SESSION PROFIT] ===== CALCULANDO SESSION PROFIT =====
2025-09-14 10:40:47.952 -03:00 [INF] [SESSION PROFIT] Contract 1 Profit: -0.35
2025-09-14 10:40:47.952 -03:00 [INF] [SESSION PROFIT] Contract 2 Profit: 0.56
2025-09-14 10:40:47.952 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:40:47.952 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:40:47.952 -03:00 [INF] [SESSION PROFIT] 🎯 Resultado líquido da dupla (pairNet): 0.21
2025-09-14 10:40:47.952 -03:00 [INF] [SESSION PROFIT] 🧮 Cálculo: -0.35 + 0.56 = 0.21
2025-09-14 10:40:47.953 -03:00 [INF] [DUAL_PERDAS] Lucro de 0.21 recuperou 0.00. Perdas restantes: 0.00
2025-09-14 10:40:47.953 -03:00 [INF] [NEW_DUAL] 💰 Perdas acumuladas - Antes: 0.00, Depois: 0.00, Mudança: 0.00
2025-09-14 10:40:47.953 -03:00 [INF] [SESSION PROFIT] Contract 2 ganhou, Contract 1 perdeu. LosingContractTypeIndex = 0
2025-09-14 10:40:47.953 -03:00 [INF] [SESSION PROFIT] SessionProfit atualizado: 0.00 + 0.21 = 0.21
2025-09-14 10:40:47.953 -03:00 [INF] [SESSION PROFIT] Diferença no SessionProfit: 0.21
2025-09-14 10:40:47.953 -03:00 [INF] [DUAL] Pair result: -0.35 + 0.56 = 0.21; SessionProfit (after): 0.21
2025-09-14 10:40:47.953 -03:00 [INF] [DUAL] Updated _previousSessionProfit to: 0.21
2025-09-14 10:40:47.953 -03:00 [INF] [DUAL] SessionProfit calculation completed - avoiding duplicate calculation
2025-09-14 10:40:47.954 -03:00 [INF] [MICRO] Transferência de micro-metas executada. Unit=0.04, TotalProfit=0.20, SessionProfit=0.01
2025-09-14 10:40:47.954 -03:00 [INF] [SESSION_END_DEBUG] Sessão NÃO encerrada - SessionProfit: 0.01, Target: 2.00
2025-09-14 10:40:47.954 -03:00 [INF] [SESSION_END_DEBUG] Diferença para target: 1.99
2025-09-14 10:40:47.954 -03:00 [INF] [SESSION_END_DEBUG] Nível atual: 0/5, Sessão: 1/1
2025-09-14 10:40:47.954 -03:00 [INF] [DUAL] ✅ Entrada dupla #1 completada - Resultado líquido: 0.21
2025-09-14 10:40:47.954 -03:00 [INF] [DUAL] 📈 LUCRO na entrada #1 - Reduzindo perdas acumuladas
2025-09-14 10:40:47.954 -03:00 [INF] [DUAL DEBUG] Continuing within session 1/1 at level 1/5
2025-09-14 10:40:47.954 -03:00 [INF] [DUAL] 🚀 Executing next dual entry FAST PATH... (Level 1/5)
2025-09-14 10:40:47.956 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:40:47.956 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:40:47.956 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 10:40:47.956 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:40:47.956
2025-09-14 10:40:47.956 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:40:47.956
2025-09-14 10:40:47.956 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.626 at 13:49:18
2025-09-14 10:40:48.170 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 214.3199ms às 10:40:48.170
2025-09-14 10:40:48.171 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 214.6495ms às 10:40:48.171
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17714.17
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.57
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.57
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.59 (0.00%)
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:48.419 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:48.420 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.57
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.22
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.22
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.94 (0.01%)
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:48.421 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:48.447 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:40:49.300 -03:00 [WRN] [DUAL WATCHDOG] Nenhuma compra detectada após conclusão do nível - tentando novamente em 400ms
2025-09-14 10:40:49.702 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:40:49.702 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:40:49.702 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 10:40:49.702 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:40:49.702
2025-09-14 10:40:49.702 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:40:49.702
2025-09-14 10:40:49.833 -03:00 [INF] [VOLATILIDADE] Preço: 5992.56300, Média: 5992.99238, Volatilidade: 0.000055
2025-09-14 10:40:49.932 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:49.933 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:49.934 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:49.942 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.22
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.22
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.22
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.94 (0.01%)
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:49.975 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:49.976 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 273.952ms às 10:40:49.976
2025-09-14 10:40:49.977 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 275.5532ms às 10:40:49.977
2025-09-14 10:40:50.229 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.22
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.62
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.62
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.54 (0.01%)
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:40:50.229 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:50.229 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:40:50.229 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.54 (0.01%)
2025-09-14 10:40:50.229 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:40:50.239 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.62
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.27
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.27
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.89 (0.01%)
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:40:50.239 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:50.239 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:40:50.239 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.89 (0.01%)
2025-09-14 10:40:50.239 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:40:51.091 -03:00 [INF] [DUAL DEBUG] ✅ ProcessDualLevelComplete completed
2025-09-14 10:40:51.836 -03:00 [INF] [VOLATILIDADE] Preço: 5992.48400, Média: 5992.97264, Volatilidade: 0.000055
2025-09-14 10:40:51.928 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:51.940 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:51.941 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:51.941 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:51.941 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:52.069 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:52.069 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:52.070 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:52.618 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.27
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.93
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.66
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.93
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.23 (0.01%)
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:40:52.619 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:52.619 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:40:52.619 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.23 (0.01%)
2025-09-14 10:40:52.619 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:40:52.675 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.93
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.93
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.93
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.23 (0.01%)
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:40:52.676 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:52.676 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:40:52.676 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.23 (0.01%)
2025-09-14 10:40:52.676 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:40:53.838 -03:00 [INF] [VOLATILIDADE] Preço: 5992.27800, Média: 5992.94874, Volatilidade: 0.000056
2025-09-14 10:40:53.923 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:53.923 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:40:53.923 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.31, Corrected=0.31, Stake=0.35, Payout=0.66
2025-09-14 10:40:53.923 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:40:53.923 - Profit: 0.31, Win: True, Exit: 5992.4840
2025-09-14 10:40:53.923 -03:00 [INF] [DUAL] Contract result received at 10:40:53.923 - WIN: true
2025-09-14 10:40:53.923 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:40:53.923 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 0
2025-09-14 10:40:53.923 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:40:53.923 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:40:53.923 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:40:53.923 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:40:53.948 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.563 at 13:49:24
2025-09-14 10:40:53.948 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.01, DualMaxLossAmount: 100.00
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:53.955 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:53.978 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:24.000, price=5992.563
2025-09-14 10:40:53.979 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:40:53.979 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.60, Corrected=-0.60, Stake=0.60, Payout=1.16
2025-09-14 10:40:53.979 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:40:53.979 - Profit: -0.60, Win: False, Exit: 5992.4840
2025-09-14 10:40:53.979 -03:00 [INF] [DUAL] Contract result received at 10:40:53.979 - WIN: false
2025-09-14 10:40:53.979 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:40:53.979 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 0
2025-09-14 10:40:53.979 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:40:53.979 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:40:53.979 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:40:53.979 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:40:54.024 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.563 at 13:49:24
2025-09-14 10:40:54.024 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:54.025 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:54.025 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.01, DualMaxLossAmount: 100.00
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:54.029 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.93
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.66
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:54.587 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:40:54.588 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:40:55.903 -03:00 [INF] [VOLATILIDADE] Preço: 5992.33900, Média: 5992.92698, Volatilidade: 0.000057
2025-09-14 10:40:55.953 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:55.953 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:40:55.953 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.60, Corrected=-0.60, Stake=0.60, Payout=1.16
2025-09-14 10:40:55.953 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:40:55.953 - Profit: -0.60, Win: False, Exit: 5992.2780
2025-09-14 10:40:55.953 -03:00 [INF] [DUAL] Contract result received at 10:40:55.953 - WIN: false
2025-09-14 10:40:55.953 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:40:55.953 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 0
2025-09-14 10:40:55.953 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:40:55.953 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:40:55.953 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:40:55.953 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:40:55.954 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.484 at 13:49:26
2025-09-14 10:40:55.954 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:49:26.000, price=5992.484
2025-09-14 10:40:55.954 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:40:55.954 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.31, Corrected=0.31, Stake=0.35, Payout=0.66
2025-09-14 10:40:55.954 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:40:55.954 - Profit: 0.31, Win: True, Exit: 5992.2780
2025-09-14 10:40:55.954 -03:00 [INF] [DUAL] Contract result received at 10:40:55.954 - WIN: true
2025-09-14 10:40:55.954 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:40:55.954 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 0
2025-09-14 10:40:55.954 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:40:55.973 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:40:55.973 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:40:55.973 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:40:55.973 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5992.484 at 13:49:26
2025-09-14 10:40:55.997 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.01, DualMaxLossAmount: 100.00
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:40:55.998 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:40:57.879 -03:00 [INF] [VOLATILIDADE] Preço: 5992.38100, Média: 5992.90928, Volatilidade: 0.000058
2025-09-14 10:40:59.837 -03:00 [INF] [VOLATILIDADE] Preço: 5992.27700, Média: 5992.89018, Volatilidade: 0.000059
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:41:00.017 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:41:01.848 -03:00 [INF] [VOLATILIDADE] Preço: 5992.32900, Média: 5992.86742, Volatilidade: 0.000059
2025-09-14 10:41:03.863 -03:00 [INF] [VOLATILIDADE] Preço: 5992.54300, Média: 5992.84744, Volatilidade: 0.000057
2025-09-14 10:41:05.868 -03:00 [INF] [VOLATILIDADE] Preço: 5992.35900, Média: 5992.82480, Volatilidade: 0.000056
2025-09-14 10:41:07.845 -03:00 [INF] [VOLATILIDADE] Preço: 5992.54400, Média: 5992.80708, Volatilidade: 0.000054
2025-09-14 10:41:09.843 -03:00 [INF] [VOLATILIDADE] Preço: 5992.43800, Média: 5992.78890, Volatilidade: 0.000054
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:41:09.982 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:41:11.843 -03:00 [INF] [VOLATILIDADE] Preço: 5992.46200, Média: 5992.77028, Volatilidade: 0.000052
2025-09-14 10:41:13.867 -03:00 [INF] [VOLATILIDADE] Preço: 5992.82200, Média: 5992.75700, Volatilidade: 0.000049
2025-09-14 10:41:16.018 -03:00 [INF] [VOLATILIDADE] Preço: 5992.81000, Média: 5992.74606, Volatilidade: 0.000047
2025-09-14 10:41:17.842 -03:00 [INF] [VOLATILIDADE] Preço: 5992.61400, Média: 5992.73242, Volatilidade: 0.000045
2025-09-14 10:41:19.890 -03:00 [INF] [VOLATILIDADE] Preço: 5992.69300, Média: 5992.72638, Volatilidade: 0.000045
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:41:20.002 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:41:21.874 -03:00 [INF] [VOLATILIDADE] Preço: 5992.64100, Média: 5992.71772, Volatilidade: 0.000044
2025-09-14 10:41:23.859 -03:00 [INF] [VOLATILIDADE] Preço: 5992.94800, Média: 5992.71844, Volatilidade: 0.000044
2025-09-14 10:41:25.864 -03:00 [INF] [VOLATILIDADE] Preço: 5993.45600, Média: 5992.73272, Volatilidade: 0.000048
2025-09-14 10:41:27.854 -03:00 [INF] [VOLATILIDADE] Preço: 5993.17600, Média: 5992.73918, Volatilidade: 0.000049
2025-09-14 10:41:29.857 -03:00 [INF] [VOLATILIDADE] Preço: 5992.99600, Média: 5992.74556, Volatilidade: 0.000049
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:41:29.989 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:41:31.871 -03:00 [INF] [VOLATILIDADE] Preço: 5993.12700, Média: 5992.75246, Volatilidade: 0.000050
2025-09-14 10:41:33.853 -03:00 [INF] [VOLATILIDADE] Preço: 5993.16300, Média: 5992.76478, Volatilidade: 0.000050
2025-09-14 10:41:35.865 -03:00 [INF] [VOLATILIDADE] Preço: 5992.99600, Média: 5992.77584, Volatilidade: 0.000050
2025-09-14 10:41:37.841 -03:00 [INF] [VOLATILIDADE] Preço: 5993.03900, Média: 5992.78852, Volatilidade: 0.000050
2025-09-14 10:41:38.862 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:41:38.862 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:41:38.862 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:41:38.862 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:41:38.862 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:41:38.862 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:41:38.862 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:41:38.862 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:41:38.863 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:41:38.863 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:41:38.863 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:41:38.863 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.01, DualMaxLossAmount: 100.00
2025-09-14 10:41:38.863 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:41:38.863 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:41:38.863 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:41:38.863 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:41:38.863 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.01, DualMaxLossAmount: 100.00
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:41:38.985 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:41:39.842 -03:00 [INF] [VOLATILIDADE] Preço: 5992.87700, Média: 5992.79296, Volatilidade: 0.000050
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17714.16
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17714.16
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.57 (0.00%)
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.96
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:41:39.990 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.01, DualMaxLossAmount: 100.00
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:41:40.218 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:41:41.467 -03:00 [INF] Application is shutting down...
2025-09-14 10:47:18.633 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-14 10:47:18.657 -03:00 [INF] Hosting environment: Production
2025-09-14 10:47:18.658 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.5
2025-09-14 10:47:19.064 -03:00 [INF] [CONFIG] Caminho do arquivo de configuração: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:47:19.083 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 10:47:19.102 -03:00 [INF] [AUTO-PAUSE] Timer de pausa automática inicializado com intervalo de 30 segundos
2025-09-14 10:47:19.102 -03:00 [INF] Conectando à API Deriv...
2025-09-14 10:47:19.513 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:47:19.513 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 10:47:19.513 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:47:19.513 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:47:19.513 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:47:19.513 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 10:47:19.513 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 10:47:19.514 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:47:19.514 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 5
2025-09-14 10:47:19.514 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:47:19.514 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: False
2025-09-14 10:47:19.514 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.00
2025-09-14 10:47:19.514 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 10:47:19.514 -03:00 [INF] [DEBUG CanExecuteBuy] AskPrice: 0
2025-09-14 10:47:19.514 -03:00 [INF] [DEBUG CanExecuteBuy] CurrentProposalId: 'null'
2025-09-14 10:47:19.514 -03:00 [INF] [DEBUG CanExecuteBuy] Normal Mode Result: False
2025-09-14 10:47:19.959 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-14 10:47:20.068 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:39:01
2025-09-14 10:47:20.078 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:47:20.078 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:47:20.081 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:47:20.081 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:47:20.084 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:47:20.084 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:47:20.085 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:47:20.085 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:47:20.088 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 10:47:20.088 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:47:20.088 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:47:20.088 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: null
2025-09-14 10:47:20.203 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:47:20.204 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:47:20.204 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: False (allowing automatic continuation)
2025-09-14 10:47:20.371 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:47:20.371 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 0.00
2025-09-14 10:47:20.371 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 0.00
2025-09-14 10:47:20.371 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:47:20.371 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:47:20.371 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:47:20.371 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:47:20.371 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 17713.59
2025-09-14 10:47:20.373 -03:00 [INF] [BALANCE] Initial balance set to: 17713.59
2025-09-14 10:47:20.374 -03:00 [INF] Status de conexão alterado para: True
2025-09-14 10:47:20.376 -03:00 [INF] [CONNECTION] Connection reestablished - restoring application state
2025-09-14 10:47:20.380 -03:00 [INF] [RESTORE] Step 1: Force loading latest configuration
2025-09-14 10:47:20.380 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 10:47:20.380 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:39:01
2025-09-14 10:47:20.381 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:47:20.381 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:47:20.381 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:47:20.381 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:47:20.381 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:47:20.381 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:47:20.381 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:47:20.381 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 10:47:20.381 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 10:47:20.381 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:47:20.381 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:47:20.381 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:47:20.381 -03:00 [INF] [RESTORE] State to restore - Market: Derived, Symbol: R_10, Contract: CALLE, DualContract: PUTE
2025-09-14 10:47:20.381 -03:00 [INF] [RESTORE] Step 2: Loading active symbols
2025-09-14 10:47:20.382 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-14 10:47:20.696 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:47:20.697 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:39:01
2025-09-14 10:47:20.755 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:47:20.755 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:47:20.755 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:47:20.755 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:47:20.755 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:47:20.755 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:47:20.900 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-14 10:47:20.901 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-14 10:47:20.901 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-14 10:47:20.901 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-14 10:47:20.901 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-14 10:47:20.901 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-14 10:47:20.901 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-14 10:47:20.901 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-14 10:47:20.901 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 10:47:20.904 -03:00 [INF] [CONFIG] Mercado restaurado: Derived
2025-09-14 10:47:20.904 -03:00 [INF] [RESTORE] Step 3: Restoring selections with retry
2025-09-14 10:47:20.905 -03:00 [INF] [RESTORE RETRY] Attempt 1/5 - Starting restoration
2025-09-14 10:47:20.906 -03:00 [INF] [RESTORE] Starting selection restoration - Market: Derived, Symbol: R_10, Contract: CALLE, Dual: PUTE
2025-09-14 10:47:20.917 -03:00 [INF] [RESTORE] Markets loaded successfully: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 10:47:20.918 -03:00 [INF] [RESTORE] ✓ Market restored: 'Derived' -> 'Derived'
2025-09-14 10:47:20.919 -03:00 [INF] [RESTORE] ✓ SubMarket restored: 'Continuous Indices' -> 'Continuous Indices'
2025-09-14 10:47:20.925 -03:00 [INF] [RESTORE] ✓ Symbol restored: 'R_10' -> 'R_10'
2025-09-14 10:47:20.926 -03:00 [INF] [RESTORE] Contract types list empty, waiting... attempt 1/25
2025-09-14 10:47:21.228 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 10:47:21.228 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-14 10:47:21.239 -03:00 [INF] [RESTORE] ✓ Main contract type restored: 'CALLE' (attempt 2)
2025-09-14 10:47:21.239 -03:00 [INF] [RESTORE] ✓ Dual contract type restored: 'PUTE' (attempt 1)
2025-09-14 10:47:21.239 -03:00 [INF] [RESTORE] Final restoration state:
2025-09-14 10:47:21.239 -03:00 [INF] [RESTORE]   Market: 'Derived' -> 'Derived' ✓
2025-09-14 10:47:21.239 -03:00 [INF] [RESTORE]   SubMarket: 'Continuous Indices' -> 'Continuous Indices' ✓
2025-09-14 10:47:21.239 -03:00 [INF] [RESTORE]   Symbol: 'R_10' -> 'R_10' ✓
2025-09-14 10:47:21.239 -03:00 [INF] [RESTORE]   Contract: 'CALLE' -> 'CALLE' ✓
2025-09-14 10:47:21.239 -03:00 [INF] [RESTORE]   DualContract: 'PUTE' -> 'PUTE' ✓
2025-09-14 10:47:21.240 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-14 10:47:21.539 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 10:47:21.541 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:47:20
2025-09-14 10:47:21.593 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 10:47:21.593 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 10:47:21.593 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 10:47:21.593 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 10:47:21.593 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 10:47:21.593 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 10:47:21.927 -03:00 [INF] [RESTORE VERIFY] Market: True, Symbol: True, Contract: True, DualContract: True
2025-09-14 10:47:21.927 -03:00 [INF] [RESTORE RETRY] ✓ Restoration successful on attempt 1
2025-09-14 10:47:30.602 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:47:30.602 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:47:30.602 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:47:30.602 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:47:30.602 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:47:30.602 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:47:30.602 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:47:30.602 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:47:30.603 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:47:30.603 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:47:30.603 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:47:30.603 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:47:30.603 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:47:30.603 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:47:30.603 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:47:30.603 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:47:30.603 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:47:39.898 -03:00 [INF] [VOLATILIDADE] Preço: 5991.86800, Média: 5991.93080, Volatilidade: 0.000013
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:47:40.571 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:47:41.890 -03:00 [INF] [VOLATILIDADE] Preço: 5991.70700, Média: 5991.91045, Volatilidade: 0.000017
2025-09-14 10:47:43.882 -03:00 [INF] [VOLATILIDADE] Preço: 5991.78400, Média: 5991.89992, Volatilidade: 0.000017
2025-09-14 10:47:45.892 -03:00 [INF] [VOLATILIDADE] Preço: 5991.71400, Média: 5991.88562, Volatilidade: 0.000018
2025-09-14 10:47:47.869 -03:00 [INF] [VOLATILIDADE] Preço: 5991.98000, Média: 5991.89236, Volatilidade: 0.000018
2025-09-14 10:47:49.870 -03:00 [INF] [VOLATILIDADE] Preço: 5991.80700, Média: 5991.88667, Volatilidade: 0.000018
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:47:50.574 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:47:51.885 -03:00 [INF] [VOLATILIDADE] Preço: 5991.47200, Média: 5991.86075, Volatilidade: 0.000024
2025-09-14 10:47:53.909 -03:00 [INF] [VOLATILIDADE] Preço: 5991.46800, Média: 5991.83765, Volatilidade: 0.000028
2025-09-14 10:47:55.882 -03:00 [INF] [VOLATILIDADE] Preço: 5991.29300, Média: 5991.80739, Volatilidade: 0.000034
2025-09-14 10:47:57.879 -03:00 [INF] [VOLATILIDADE] Preço: 5990.81500, Média: 5991.75516, Volatilidade: 0.000050
2025-09-14 10:47:59.863 -03:00 [INF] [VOLATILIDADE] Preço: 5990.82000, Média: 5991.70840, Volatilidade: 0.000059
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:48:00.607 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:48:01.886 -03:00 [INF] [VOLATILIDADE] Preço: 5991.08500, Média: 5991.67871, Volatilidade: 0.000062
2025-09-14 10:48:03.916 -03:00 [INF] [VOLATILIDADE] Preço: 5991.00900, Média: 5991.64827, Volatilidade: 0.000065
2025-09-14 10:48:05.911 -03:00 [INF] [VOLATILIDADE] Preço: 5991.14900, Média: 5991.62657, Volatilidade: 0.000066
2025-09-14 10:48:07.892 -03:00 [INF] [VOLATILIDADE] Preço: 5991.19500, Média: 5991.60858, Volatilidade: 0.000066
2025-09-14 10:48:09.876 -03:00 [INF] [VOLATILIDADE] Preço: 5991.25000, Média: 5991.59424, Volatilidade: 0.000066
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:48:10.588 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:48:11.886 -03:00 [INF] [VOLATILIDADE] Preço: 5991.36000, Média: 5991.58523, Volatilidade: 0.000065
2025-09-14 10:48:13.884 -03:00 [INF] [VOLATILIDADE] Preço: 5991.37000, Média: 5991.57726, Volatilidade: 0.000064
2025-09-14 10:48:15.888 -03:00 [INF] [VOLATILIDADE] Preço: 5991.18500, Média: 5991.56325, Volatilidade: 0.000064
2025-09-14 10:48:17.873 -03:00 [INF] [VOLATILIDADE] Preço: 5991.37700, Média: 5991.55683, Volatilidade: 0.000063
2025-09-14 10:48:19.848 -03:00 [INF] [VOLATILIDADE] Preço: 5991.49500, Média: 5991.55477, Volatilidade: 0.000062
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:48:20.601 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:48:21.896 -03:00 [INF] [VOLATILIDADE] Preço: 5991.46700, Média: 5991.55194, Volatilidade: 0.000061
2025-09-14 10:48:23.891 -03:00 [INF] [VOLATILIDADE] Preço: 5991.27900, Média: 5991.54341, Volatilidade: 0.000061
2025-09-14 10:48:25.915 -03:00 [INF] [VOLATILIDADE] Preço: 5991.36400, Média: 5991.53797, Volatilidade: 0.000060
2025-09-14 10:48:27.891 -03:00 [INF] [VOLATILIDADE] Preço: 5991.49200, Média: 5991.53662, Volatilidade: 0.000059
2025-09-14 10:48:29.857 -03:00 [INF] [VOLATILIDADE] Preço: 5991.44200, Média: 5991.53391, Volatilidade: 0.000058
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:48:30.580 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:48:31.920 -03:00 [INF] [VOLATILIDADE] Preço: 5991.64400, Média: 5991.53697, Volatilidade: 0.000058
2025-09-14 10:48:33.908 -03:00 [INF] [VOLATILIDADE] Preço: 5991.66400, Média: 5991.54041, Volatilidade: 0.000057
2025-09-14 10:48:35.900 -03:00 [INF] [VOLATILIDADE] Preço: 5991.61500, Média: 5991.54237, Volatilidade: 0.000056
2025-09-14 10:48:37.909 -03:00 [INF] [VOLATILIDADE] Preço: 5991.39000, Média: 5991.53846, Volatilidade: 0.000056
2025-09-14 10:48:39.882 -03:00 [INF] [VOLATILIDADE] Preço: 5991.61000, Média: 5991.54025, Volatilidade: 0.000055
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:48:40.602 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:48:41.909 -03:00 [INF] [VOLATILIDADE] Preço: 5991.58400, Média: 5991.54132, Volatilidade: 0.000054
2025-09-14 10:48:43.896 -03:00 [INF] [VOLATILIDADE] Preço: 5991.73900, Média: 5991.54602, Volatilidade: 0.000054
2025-09-14 10:48:45.893 -03:00 [INF] [VOLATILIDADE] Preço: 5991.67300, Média: 5991.54898, Volatilidade: 0.000053
2025-09-14 10:48:47.894 -03:00 [INF] [VOLATILIDADE] Preço: 5991.53900, Média: 5991.54875, Volatilidade: 0.000053
2025-09-14 10:48:49.879 -03:00 [INF] [VOLATILIDADE] Preço: 5991.56800, Média: 5991.54918, Volatilidade: 0.000052
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:48:50.602 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:48:51.914 -03:00 [INF] [VOLATILIDADE] Preço: 5991.68200, Média: 5991.55207, Volatilidade: 0.000052
2025-09-14 10:48:53.897 -03:00 [INF] [VOLATILIDADE] Preço: 5991.47100, Média: 5991.55034, Volatilidade: 0.000051
2025-09-14 10:48:55.867 -03:00 [INF] [VOLATILIDADE] Preço: 5991.19900, Média: 5991.54302, Volatilidade: 0.000051
2025-09-14 10:48:57.933 -03:00 [INF] [VOLATILIDADE] Preço: 5991.47700, Média: 5991.54167, Volatilidade: 0.000051
2025-09-14 10:48:59.886 -03:00 [INF] [VOLATILIDADE] Preço: 5991.53800, Média: 5991.54160, Volatilidade: 0.000050
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:49:00.630 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:49:01.885 -03:00 [INF] [VOLATILIDADE] Preço: 5991.62200, Média: 5991.53688, Volatilidade: 0.000050
2025-09-14 10:49:03.923 -03:00 [INF] [VOLATILIDADE] Preço: 5991.46700, Média: 5991.52648, Volatilidade: 0.000049
2025-09-14 10:49:05.979 -03:00 [INF] [VOLATILIDADE] Preço: 5991.54400, Média: 5991.51690, Volatilidade: 0.000047
2025-09-14 10:49:07.918 -03:00 [INF] [VOLATILIDADE] Preço: 5991.71200, Média: 5991.50902, Volatilidade: 0.000045
2025-09-14 10:49:09.869 -03:00 [INF] [VOLATILIDADE] Preço: 5991.85800, Média: 5991.50814, Volatilidade: 0.000045
2025-09-14 10:49:10.594 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:49:10.595 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:49:11.901 -03:00 [INF] [VOLATILIDADE] Preço: 5991.70900, Média: 5991.50394, Volatilidade: 0.000044
2025-09-14 10:49:13.926 -03:00 [INF] [VOLATILIDADE] Preço: 5991.60900, Média: 5991.49880, Volatilidade: 0.000043
2025-09-14 10:49:15.908 -03:00 [INF] [VOLATILIDADE] Preço: 5991.52200, Média: 5991.49066, Volatilidade: 0.000042
2025-09-14 10:49:17.894 -03:00 [INF] [VOLATILIDADE] Preço: 5991.45900, Média: 5991.48284, Volatilidade: 0.000041
2025-09-14 10:49:19.849 -03:00 [INF] [VOLATILIDADE] Preço: 5991.31500, Média: 5991.47178, Volatilidade: 0.000040
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:49:20.640 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:49:21.901 -03:00 [INF] [VOLATILIDADE] Preço: 5991.14500, Média: 5991.46054, Volatilidade: 0.000041
2025-09-14 10:49:23.894 -03:00 [INF] [VOLATILIDADE] Preço: 5991.26500, Média: 5991.45016, Volatilidade: 0.000040
2025-09-14 10:49:25.902 -03:00 [INF] [VOLATILIDADE] Preço: 5991.28500, Média: 5991.44158, Volatilidade: 0.000040
2025-09-14 10:49:27.877 -03:00 [INF] [VOLATILIDADE] Preço: 5991.39500, Média: 5991.42988, Volatilidade: 0.000038
2025-09-14 10:49:29.924 -03:00 [INF] [VOLATILIDADE] Preço: 5991.59800, Média: 5991.42570, Volatilidade: 0.000037
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:49:30.608 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:49:31.878 -03:00 [INF] [VOLATILIDADE] Preço: 5991.62700, Média: 5991.42880, Volatilidade: 0.000037
2025-09-14 10:49:33.924 -03:00 [INF] [VOLATILIDADE] Preço: 5991.52100, Média: 5991.42986, Volatilidade: 0.000037
2025-09-14 10:49:35.916 -03:00 [INF] [VOLATILIDADE] Preço: 5991.65600, Média: 5991.43712, Volatilidade: 0.000038
2025-09-14 10:49:37.896 -03:00 [INF] [VOLATILIDADE] Preço: 5991.58700, Média: 5991.45256, Volatilidade: 0.000035
2025-09-14 10:49:39.881 -03:00 [INF] [VOLATILIDADE] Preço: 5991.45500, Média: 5991.46526, Volatilidade: 0.000031
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:49:40.617 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:49:41.892 -03:00 [INF] [VOLATILIDADE] Preço: 5991.50600, Média: 5991.47368, Volatilidade: 0.000030
2025-09-14 10:49:43.896 -03:00 [INF] [VOLATILIDADE] Preço: 5991.14000, Média: 5991.47630, Volatilidade: 0.000029
2025-09-14 10:49:45.872 -03:00 [INF] [VOLATILIDADE] Preço: 5991.14700, Média: 5991.47626, Volatilidade: 0.000029
2025-09-14 10:49:47.884 -03:00 [INF] [VOLATILIDADE] Preço: 5991.17700, Média: 5991.47590, Volatilidade: 0.000029
2025-09-14 10:49:49.900 -03:00 [INF] [VOLATILIDADE] Preço: 5991.07000, Média: 5991.47230, Volatilidade: 0.000030
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:49:50.614 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:49:51.888 -03:00 [INF] [VOLATILIDADE] Preço: 5991.03400, Média: 5991.46578, Volatilidade: 0.000032
2025-09-14 10:49:53.877 -03:00 [INF] [VOLATILIDADE] Preço: 5991.06300, Média: 5991.45964, Volatilidade: 0.000033
2025-09-14 10:49:55.881 -03:00 [INF] [VOLATILIDADE] Preço: 5990.97500, Média: 5991.45544, Volatilidade: 0.000034
2025-09-14 10:49:57.879 -03:00 [INF] [VOLATILIDADE] Preço: 5990.98300, Média: 5991.44756, Volatilidade: 0.000036
2025-09-14 10:49:59.860 -03:00 [INF] [VOLATILIDADE] Preço: 5990.80300, Média: 5991.43372, Volatilidade: 0.000039
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:50:00.630 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:50:01.858 -03:00 [INF] [VOLATILIDADE] Preço: 5990.92400, Média: 5991.42286, Volatilidade: 0.000041
2025-09-14 10:50:03.871 -03:00 [INF] [VOLATILIDADE] Preço: 5990.86900, Média: 5991.41466, Volatilidade: 0.000043
2025-09-14 10:50:05.908 -03:00 [INF] [VOLATILIDADE] Preço: 5990.94900, Média: 5991.40636, Volatilidade: 0.000044
2025-09-14 10:50:07.891 -03:00 [INF] [VOLATILIDADE] Preço: 5991.14700, Média: 5991.39946, Volatilidade: 0.000044
2025-09-14 10:50:09.883 -03:00 [INF] [VOLATILIDADE] Preço: 5991.29900, Média: 5991.39660, Volatilidade: 0.000044
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:50:10.629 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:50:11.924 -03:00 [INF] [VOLATILIDADE] Preço: 5991.45500, Média: 5991.39282, Volatilidade: 0.000044
2025-09-14 10:50:13.910 -03:00 [INF] [VOLATILIDADE] Preço: 5991.34700, Média: 5991.38648, Volatilidade: 0.000044
2025-09-14 10:50:15.952 -03:00 [INF] [VOLATILIDADE] Preço: 5991.26100, Média: 5991.37940, Volatilidade: 0.000043
2025-09-14 10:50:17.877 -03:00 [INF] [VOLATILIDADE] Preço: 5991.24700, Média: 5991.37654, Volatilidade: 0.000043
2025-09-14 10:50:19.854 -03:00 [INF] [VOLATILIDADE] Preço: 5991.21600, Média: 5991.36866, Volatilidade: 0.000043
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:50:20.602 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:50:21.873 -03:00 [INF] [VOLATILIDADE] Preço: 5991.01700, Média: 5991.35732, Volatilidade: 0.000044
2025-09-14 10:50:23.931 -03:00 [INF] [VOLATILIDADE] Preço: 5991.09400, Média: 5991.34442, Volatilidade: 0.000043
2025-09-14 10:50:25.888 -03:00 [INF] [VOLATILIDADE] Preço: 5991.05700, Média: 5991.33210, Volatilidade: 0.000043
2025-09-14 10:50:27.882 -03:00 [INF] [VOLATILIDADE] Preço: 5990.99200, Média: 5991.32116, Volatilidade: 0.000043
2025-09-14 10:50:29.853 -03:00 [INF] [VOLATILIDADE] Preço: 5990.77900, Média: 5991.30538, Volatilidade: 0.000045
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:50:30.583 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:50:31.896 -03:00 [INF] [VOLATILIDADE] Preço: 5990.76600, Média: 5991.28706, Volatilidade: 0.000046
2025-09-14 10:50:33.885 -03:00 [INF] [VOLATILIDADE] Preço: 5990.95500, Média: 5991.27674, Volatilidade: 0.000046
2025-09-14 10:50:35.888 -03:00 [INF] [VOLATILIDADE] Preço: 5990.97200, Média: 5991.27220, Volatilidade: 0.000047
2025-09-14 10:50:37.888 -03:00 [INF] [VOLATILIDADE] Preço: 5990.76200, Média: 5991.25790, Volatilidade: 0.000048
2025-09-14 10:50:39.881 -03:00 [INF] [VOLATILIDADE] Preço: 5990.59400, Média: 5991.23902, Volatilidade: 0.000050
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:50:40.589 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:50:41.898 -03:00 [INF] [VOLATILIDADE] Preço: 5990.41000, Média: 5991.21478, Volatilidade: 0.000052
2025-09-14 10:50:43.876 -03:00 [INF] [VOLATILIDADE] Preço: 5990.42100, Média: 5991.19386, Volatilidade: 0.000055
2025-09-14 10:50:45.913 -03:00 [INF] [VOLATILIDADE] Preço: 5990.11500, Média: 5991.16528, Volatilidade: 0.000060
2025-09-14 10:50:47.899 -03:00 [INF] [VOLATILIDADE] Preço: 5990.08300, Média: 5991.13270, Volatilidade: 0.000064
2025-09-14 10:50:49.923 -03:00 [INF] [VOLATILIDADE] Preço: 5990.45000, Média: 5991.10454, Volatilidade: 0.000063
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:50:50.611 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:50:51.889 -03:00 [INF] [VOLATILIDADE] Preço: 5990.47500, Média: 5991.07986, Volatilidade: 0.000063
2025-09-14 10:50:53.892 -03:00 [INF] [VOLATILIDADE] Preço: 5990.40300, Média: 5991.05574, Volatilidade: 0.000064
2025-09-14 10:50:55.882 -03:00 [INF] [VOLATILIDADE] Preço: 5990.38900, Média: 5991.03308, Volatilidade: 0.000065
2025-09-14 10:50:57.873 -03:00 [INF] [VOLATILIDADE] Preço: 5990.29900, Média: 5991.00988, Volatilidade: 0.000066
2025-09-14 10:50:59.889 -03:00 [INF] [VOLATILIDADE] Preço: 5990.42500, Média: 5990.99208, Volatilidade: 0.000067
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:00.598 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:01.964 -03:00 [INF] [VOLATILIDADE] Preço: 5990.60300, Média: 5990.98124, Volatilidade: 0.000068
2025-09-14 10:51:03.924 -03:00 [INF] [VOLATILIDADE] Preço: 5990.26400, Média: 5990.96122, Volatilidade: 0.000069
2025-09-14 10:51:05.884 -03:00 [INF] [VOLATILIDADE] Preço: 5990.37100, Média: 5990.94294, Volatilidade: 0.000070
2025-09-14 10:51:07.885 -03:00 [INF] [VOLATILIDADE] Preço: 5990.65600, Média: 5990.92816, Volatilidade: 0.000070
2025-09-14 10:51:09.876 -03:00 [INF] [VOLATILIDADE] Preço: 5990.61600, Média: 5990.90852, Volatilidade: 0.000068
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:10.587 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:11.867 -03:00 [INF] [VOLATILIDADE] Preço: 5990.55600, Média: 5990.88710, Volatilidade: 0.000067
2025-09-14 10:51:13.883 -03:00 [INF] [VOLATILIDADE] Preço: 5990.72900, Média: 5990.87126, Volatilidade: 0.000065
2025-09-14 10:51:15.894 -03:00 [INF] [VOLATILIDADE] Preço: 5990.66200, Média: 5990.85138, Volatilidade: 0.000062
2025-09-14 10:51:17.912 -03:00 [INF] [VOLATILIDADE] Preço: 5990.59500, Média: 5990.83154, Volatilidade: 0.000060
2025-09-14 10:51:19.869 -03:00 [INF] [VOLATILIDADE] Preço: 5990.57500, Média: 5990.81394, Volatilidade: 0.000059
2025-09-14 10:51:21.519 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.59
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.59
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:21.520 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:21.530 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:51:21.531 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:51:21.778 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:51:21.909 -03:00 [INF] [VOLATILIDADE] Preço: 5990.43800, Média: 5990.79258, Volatilidade: 0.000057
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:51:22.000 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:51:22.002 -03:00 [INF] [BUY] ExecuteBuyCommand called - IsDualEnabled: True
2025-09-14 10:51:22.002 -03:00 [INF] [BUY] Dual mode detected, calling ExecuteDualEntryCommand
2025-09-14 10:51:22.005 -03:00 [INF] [DUAL DEBUG] 🚀 ExecuteDualEntryCommand started
2025-09-14 10:51:22.007 -03:00 [INF] [DUAL DEBUG] Connection check passed
2025-09-14 10:51:22.007 -03:00 [INF] [DUAL DEBUG] Timing check bypassed for dual mode (no-delay)
2025-09-14 10:51:22.007 -03:00 [INF] [DUAL DEBUG] Validating contract types - ContractType: CALLE, DualContractType: PUTE, Symbol: R_10
2025-09-14 10:51:22.007 -03:00 [INF] [DUAL] Iniciando primeira sessão dual - Session 1
2025-09-14 10:51:22.007 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 0/5, Session 1/1
2025-09-14 10:51:22.008 -03:00 [INF] [NEW_DUAL] 🚀 Chamando CalculateNewDualStakes...
2025-09-14 10:51:22.009 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:51:22.009 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:51:22.009 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 10:51:22.009 -03:00 [INF] [NEW_DUAL] ✅ Calculated stakes using new formulas - X: 0.35, Y: 0.60
2025-09-14 10:51:22.009 -03:00 [INF] [NEW_DUAL] 📊 Parameters - Lucro Alvo: 2.00, Alfa: 0.50, Lucro Base: 0.04, R(y/x): 1.70
2025-09-14 10:51:22.009 -03:00 [INF] [NEW_DUAL] 💰 Perdas Acumuladas: 0.00
2025-09-14 10:51:22.010 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:51:22.019 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:51:22.020 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 10:51:22.020 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:51:22.020 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:51:22.216 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=b425cef4-dea8-a93c-d600-e82f8c92f971, AskPrice=0.35, Payout=0.66
2025-09-14 10:51:22.216 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 10:51:22.422 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=e67672e9-d734-ba8e-68e4-e04667943f7e, AskPrice=0.60, Payout=1.16
2025-09-14 10:51:22.422 -03:00 [INF] [NEW_DUAL] Payouts - Contrato 1: 0.66, Contrato 2: 1.16
2025-09-14 10:51:22.422 -03:00 [INF] [NEW_DUAL] Final stakes - Contrato 1: 0.60, Contrato 2: 0.35
2025-09-14 10:51:22.422 -03:00 [INF] [DUAL] Primeira entrada - Random choice: Contrato principal MENOR stake
2025-09-14 10:51:22.422 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0.35, Lower: 0.60
2025-09-14 10:51:22.422 -03:00 [INF] [DUAL] Stake MAIOR (0.60) vai para: Lower
2025-09-14 10:51:22.422 -03:00 [INF] [DUAL] Stakes finais - Higher: 0.35, Lower: 0.60
2025-09-14 10:51:22.422 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 10:51:22.693 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=b425cef4-dea8-a93c-d600-e82f8c92f971, AskPrice=0.35, Payout=0.66
2025-09-14 10:51:22.693 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 10:51:22.897 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=e67672e9-d734-ba8e-68e4-e04667943f7e, AskPrice=0.60, Payout=1.16
2025-09-14 10:51:22.897 -03:00 [INF] [DUAL] Payouts - Contrato 1 (Higher): 0.66, Contrato 2 (Lower): 1.16
2025-09-14 10:51:22.897 -03:00 [INF] [DUAL ENTRY] ===== INICIANDO COMPRAS DUAIS =====
2025-09-14 10:51:22.897 -03:00 [INF] [DUAL ENTRY] Saldo antes das compras: 17713.59
2025-09-14 10:51:22.897 -03:00 [INF] [DUAL ENTRY] Active Exposure antes: 0.00
2025-09-14 10:51:22.897 -03:00 [INF] [DUAL ENTRY] Total stake a ser debitado: 0.95
2025-09-14 10:51:22.897 -03:00 [INF] [DUAL] INTENT BUY -> C1=Higher, Stake=0.35, ProposalId=b425cef4-dea8-a93c-d600-e82f8c92f971
2025-09-14 10:51:22.897 -03:00 [INF] [DUAL] INTENT BUY -> C2=Lower, Stake=0.60, ProposalId=e67672e9-d734-ba8e-68e4-e04667943f7e
2025-09-14 10:51:23.161 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:23.161 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.59
2025-09-14 10:51:23.161 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:23.161 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:51:23.161 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:23.161 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:23.161 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.24
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.24
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.35 (0.00%)
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:23.162 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:23.169 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:51:23.178 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:51:23.178 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-14 10:51:23.178 -03:00 [INF] [DUAL ENTRY] Saldo após compras: 17713.59
2025-09-14 10:51:23.178 -03:00 [INF] [DUAL ENTRY] Active Exposure após: 0.00
2025-09-14 10:51:23.178 -03:00 [INF] [DUAL ENTRY] Diferença no saldo: 0.00
2025-09-14 10:51:23.178 -03:00 [INF] [DUAL ENTRY] Diferença no Active Exposure: 0.00
2025-09-14 10:51:23.178 -03:00 [INF] [DUAL ENTRY] Contract 1 ID: ************
2025-09-14 10:51:23.178 -03:00 [INF] [DUAL ENTRY] Contract 2 ID: ************
2025-09-14 10:51:23.607 -03:00 [INF] New maximum stake recorded: 0.35
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Contract Type: Higher
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Stake: 0.35
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Payout: 0.66
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Entries count: 0 -> 1
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Saldo: 17713.59 -> 17713.24
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Active Exposure: 0.00 -> 0.35
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.35
2025-09-14 10:51:23.608 -03:00 [INF] New maximum stake recorded: 0.60
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Contract Type: Lower
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Stake: 0.60
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Payout: 1.16
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Entries count: 1 -> 2
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Saldo: 17713.24 -> 17712.64
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Active Exposure: 0.35 -> 0.95
2025-09-14 10:51:23.608 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.60
2025-09-14 10:51:23.608 -03:00 [INF] [DUAL] Compra dupla executada no nível 0 (máximo: 5)
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.24
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17712.64
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.64
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.64
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17712.64
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:51:23.609 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:51:23.666 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 2
2025-09-14 10:51:23.667 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:51:23.907 -03:00 [INF] [VOLATILIDADE] Preço: 5990.48700, Média: 5990.77952, Volatilidade: 0.000057
2025-09-14 10:51:23.915 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:23.916 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:23.917 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:23.933 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:23.933 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:23.934 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:23.934 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:23.934 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:25.947 -03:00 [INF] [VOLATILIDADE] Preço: 5990.53600, Média: 5990.76730, Volatilidade: 0.000056
2025-09-14 10:51:25.948 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:25.948 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:25.948 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:25.952 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:25.965 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:25.965 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:25.965 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:25.966 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.64
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17712.64
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.30
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.66
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.30
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17712.64
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.66 (0.00%)
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:51:26.304 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.30
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17712.64
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.30
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.30
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17712.64
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.66 (0.00%)
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 10:51:26.517 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:27.899 -03:00 [INF] [VOLATILIDADE] Preço: 5990.27700, Média: 5990.74930, Volatilidade: 0.000057
2025-09-14 10:51:27.899 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:27.902 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:27.902 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:51:27.903 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.31, Corrected=0.31, Stake=0.35, Payout=0.66
2025-09-14 10:51:27.904 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:51:27.903 - Profit: 0.31, Win: True, Exit: 5990.5360
2025-09-14 10:51:27.904 -03:00 [INF] [DUAL] Contract result received at 10:51:27.904 - WIN: true
2025-09-14 10:51:27.904 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-14 10:51:27.904 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 10:51:27.907 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Contract Type: Higher
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Stake: 0.35
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Payout: 0.66
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Entry Price: 5990.4870
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Exit Price: 5990.5360
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Raw Profit: 0.310000
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Rounded Profit: 0.31
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Result: WIN
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 10:51:27.907 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 10:51:27.907 -03:00 [INF] [BALANCE] Before transaction: 17712.99
2025-09-14 10:51:27.907 -03:00 [INF] Profit Table updated for contract ************: Profit=0.31, ExitPrice=5990.536, ExitTime=14:00:00
2025-09-14 10:51:27.908 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 10:51:27.908 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 10:51:27.908 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 10:51:27.908 -03:00 [INF] [SESSION PROFIT] Profit do contrato: 0.31
2025-09-14 10:51:27.908 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:51:27.908 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:51:27.908 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit 0.31
2025-09-14 10:51:27.908 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: False, Contract2Completed: False
2025-09-14 10:51:27.908 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 10:51:27.908 -03:00 [INF] [DUAL DEBUG] Contract index: 0
2025-09-14 10:51:27.908 -03:00 [INF] [DUAL] Contract 1 finished: Stake=0.35, Profit=0.31
2025-09-14 10:51:27.908 -03:00 [INF] [SESSION PROFIT] Contract 1 - Stake: 0.35, Profit: 0.31
2025-09-14 10:51:27.908 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: True, Contract2Completed: False
2025-09-14 10:51:27.908 -03:00 [INF] [DUAL DEBUG] Waiting for other contract to complete...
2025-09-14 10:51:27.908 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:27.909 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 13:59:58.000, price=5990.487
2025-09-14 10:51:27.910 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:27.910 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:51:27.910 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.60, Corrected=-0.60, Stake=0.60, Payout=1.16
2025-09-14 10:51:27.910 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:51:27.910 - Profit: -0.60, Win: False, Exit: 5990.5360
2025-09-14 10:51:27.910 -03:00 [INF] [DUAL] Contract result received at 10:51:27.910 - WIN: false
2025-09-14 10:51:27.910 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-14 10:51:27.910 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 10:51:27.910 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Contract Type: Lower
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Stake: 0.60
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Payout: 1.16
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Entry Price: 5990.4870
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Exit Price: 5990.5360
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Raw Profit: -0.600000
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Rounded Profit: -0.60
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Result: LOSS
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 10:51:27.911 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 10:51:27.911 -03:00 [INF] [BALANCE] Before transaction: 17713.59
2025-09-14 10:51:27.911 -03:00 [INF] Profit Table updated for contract ************: Profit=-0.60, ExitPrice=5990.536, ExitTime=14:00:00
2025-09-14 10:51:27.911 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 10:51:27.911 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 10:51:27.911 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 10:51:27.911 -03:00 [INF] [SESSION PROFIT] Profit do contrato: -0.60
2025-09-14 10:51:27.911 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:51:27.911 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:51:27.911 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit -0.60
2025-09-14 10:51:27.911 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: True, Contract2Completed: False
2025-09-14 10:51:27.911 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 10:51:27.911 -03:00 [INF] [DUAL DEBUG] Contract index: 1
2025-09-14 10:51:27.911 -03:00 [INF] [DUAL] Contract 2 finished: Stake=0.60, Profit=-0.60
2025-09-14 10:51:27.911 -03:00 [INF] [SESSION PROFIT] Contract 2 - Stake: 0.60, Profit: -0.60
2025-09-14 10:51:27.911 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: True, Contract2Completed: True
2025-09-14 10:51:27.911 -03:00 [INF] [DUAL DEBUG] ✅ Both contracts completed in OnContractFinished - calling ProcessDualLevelComplete
2025-09-14 10:51:27.911 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.487 at 13:59:58
2025-09-14 10:51:27.911 -03:00 [INF] [DUAL DEBUG] 🚀 Executing ProcessDualLevelComplete (FAST ASYNC)...
2025-09-14 10:51:27.913 -03:00 [INF] [DUAL] 🎯 ProcessDualLevelComplete STARTED - Level 0, SessionProfit: 0.00
2025-09-14 10:51:27.913 -03:00 [INF] [DUAL REAL RESULT] Primeira entrada (Level 0) - Contract1=0.31, Contract2=-0.60
2025-09-14 10:51:27.913 -03:00 [INF] [SESSION PROFIT] ===== CALCULANDO SESSION PROFIT =====
2025-09-14 10:51:27.913 -03:00 [INF] [SESSION PROFIT] Contract 1 Profit: 0.31
2025-09-14 10:51:27.913 -03:00 [INF] [SESSION PROFIT] Contract 2 Profit: -0.60
2025-09-14 10:51:27.913 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 10:51:27.913 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 10:51:27.913 -03:00 [INF] [SESSION PROFIT] 🎯 Resultado líquido da dupla (pairNet): -0.29
2025-09-14 10:51:27.913 -03:00 [INF] [SESSION PROFIT] 🧮 Cálculo: 0.31 + -0.60 = -0.29
2025-09-14 10:51:27.913 -03:00 [INF] [DUAL_PERDAS] Prejuízo de 0.29 adicionado. Total acumulado: 0.29
2025-09-14 10:51:27.913 -03:00 [INF] [NEW_DUAL] 💰 Perdas acumuladas - Antes: 0.00, Depois: 0.29, Mudança: 0.29
2025-09-14 10:51:27.913 -03:00 [INF] [SESSION PROFIT] Contract 1 ganhou, Contract 2 perdeu. LosingContractTypeIndex = 1
2025-09-14 10:51:27.914 -03:00 [INF] [SESSION PROFIT] SessionProfit atualizado: 0.00 + -0.29 = -0.29
2025-09-14 10:51:27.914 -03:00 [INF] [SESSION PROFIT] Diferença no SessionProfit: -0.29
2025-09-14 10:51:27.914 -03:00 [INF] [DUAL] Pair result: 0.31 + -0.60 = -0.29; SessionProfit (after): -0.29
2025-09-14 10:51:27.914 -03:00 [INF] [DUAL] Updated _previousSessionProfit to: -0.29
2025-09-14 10:51:27.914 -03:00 [INF] [DUAL] SessionProfit calculation completed - avoiding duplicate calculation
2025-09-14 10:51:27.914 -03:00 [INF] [SESSION_END_DEBUG] Sessão NÃO encerrada - SessionProfit: -0.29, Target: 2.00
2025-09-14 10:51:27.914 -03:00 [INF] [SESSION_END_DEBUG] Diferença para target: 2.29
2025-09-14 10:51:27.914 -03:00 [INF] [SESSION_END_DEBUG] Nível atual: 0/5, Sessão: 1/1
2025-09-14 10:51:27.914 -03:00 [WRN] [SESSION_END_DEBUG] ⚠️ PREJUÍZO SIGNIFICATIVO DETECTADO: -0.29 (limite: -0.03)
2025-09-14 10:51:27.914 -03:00 [WRN] [SESSION_END_DEBUG] Sessão deveria ser encerrada por prejuízo, mas proteções podem estar impedindo
2025-09-14 10:51:27.914 -03:00 [INF] [DUAL] ✅ Entrada dupla #1 completada - Resultado líquido: -0.29
2025-09-14 10:51:27.914 -03:00 [INF] [DUAL] 📉 PREJUÍZO na entrada #1 - Acumulando perdas para recuperação
2025-09-14 10:51:27.914 -03:00 [INF] [DUAL DEBUG] Continuing within session 1/1 at level 1/5
2025-09-14 10:51:27.914 -03:00 [INF] [DUAL] 🚀 Executing next dual entry FAST PATH... (Level 1/5)
2025-09-14 10:51:27.916 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 10:51:27.916 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.29, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 10:51:27.916 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.37, y=0.62, L=0.1846, P=0.2969, k=1.608
2025-09-14 10:51:27.916 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:51:27.916
2025-09-14 10:51:27.916 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 10:51:27.916
2025-09-14 10:51:28.130 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 214.195ms às 10:51:28.130
2025-09-14 10:51:28.130 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 214.3104ms às 10:51:28.130
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.30
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: -0.29
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.93
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.37
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.93
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.66 (0.00%)
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: -0.29
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:28.390 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.93
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: -0.29
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.31
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.62
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.31
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.28 (0.01%)
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: -0.29
2025-09-14 10:51:28.399 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:28.399 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:51:28.399 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.28 (0.01%)
2025-09-14 10:51:28.399 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:51:28.406 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:51:28.430 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 10:51:29.382 -03:00 [INF] [DUAL DEBUG] ✅ ProcessDualLevelComplete completed
2025-09-14 10:51:29.908 -03:00 [INF] [VOLATILIDADE] Preço: 5990.16400, Média: 5990.73118, Volatilidade: 0.000058
2025-09-14 10:51:29.941 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:29.942 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:29.942 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:29.942 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.31
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: -0.29
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.31
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.31
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.28 (0.01%)
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: -0.29
2025-09-14 10:51:30.574 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:30.574 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 10:51:30.574 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.28 (0.01%)
2025-09-14 10:51:30.574 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 10:51:31.893 -03:00 [INF] [VOLATILIDADE] Preço: 5990.02300, Média: 5990.71096, Volatilidade: 0.000059
2025-09-14 10:51:31.936 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:31.936 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:31.936 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:31.936 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.31
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: -0.29
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.51
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 1.20
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.51
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.08 (0.00%)
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: -0.29
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:32.258 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:32.685 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:32.685 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.51
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: -0.29
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.51
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.51
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.08 (0.00%)
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: -0.29
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:32.686 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:33.902 -03:00 [INF] [VOLATILIDADE] Preço: 5989.94100, Média: 5990.68852, Volatilidade: 0.000062
2025-09-14 10:51:33.927 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:33.928 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:51:33.928 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.37, Corrected=-0.37, Stake=0.37, Payout=0.70
2025-09-14 10:51:33.928 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:51:33.928 - Profit: -0.37, Win: False, Exit: 5990.0230
2025-09-14 10:51:33.928 -03:00 [INF] [DUAL] Contract result received at 10:51:33.928 - WIN: false
2025-09-14 10:51:33.928 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:51:33.928 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 0
2025-09-14 10:51:33.928 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:51:33.928 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:51:33.928 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:51:33.928 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:51:33.944 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.164 at 14:00:04
2025-09-14 10:51:33.944 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:00:04.000, price=5990.164
2025-09-14 10:51:33.945 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 10:51:33.945 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.58, Corrected=0.58, Stake=0.62, Payout=1.20
2025-09-14 10:51:33.945 -03:00 [INF] [FALLBACK] Contrato ************ processado às 10:51:33.945 - Profit: 0.58, Win: True, Exit: 5990.0230
2025-09-14 10:51:33.945 -03:00 [INF] [DUAL] Contract result received at 10:51:33.945 - WIN: true
2025-09-14 10:51:33.945 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 10:51:33.945 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 0
2025-09-14 10:51:33.945 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 10:51:33.945 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 10:51:33.945 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 10:51:33.945 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 10:51:33.969 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.164 at 14:00:04
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: -0.29, DualMaxLossAmount: 100.00
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 10:51:33.973 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 10:51:35.885 -03:00 [INF] [VOLATILIDADE] Preço: 5990.01300, Média: 5990.66928, Volatilidade: 0.000063
2025-09-14 10:51:37.884 -03:00 [INF] [VOLATILIDADE] Preço: 5990.16500, Média: 5990.65292, Volatilidade: 0.000064
2025-09-14 10:51:39.899 -03:00 [INF] [VOLATILIDADE] Preço: 5990.27000, Média: 5990.64226, Volatilidade: 0.000064
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.51
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: -0.29
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.51
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.51
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.08 (0.00%)
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: -0.29
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:40.599 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:41.887 -03:00 [INF] [VOLATILIDADE] Preço: 5990.23100, Média: 5990.62840, Volatilidade: 0.000065
2025-09-14 10:51:43.881 -03:00 [INF] [VOLATILIDADE] Preço: 5990.66600, Média: 5990.62434, Volatilidade: 0.000064
2025-09-14 10:51:45.891 -03:00 [INF] [VOLATILIDADE] Preço: 5990.73500, Média: 5990.62006, Volatilidade: 0.000064
2025-09-14 10:51:47.892 -03:00 [INF] [VOLATILIDADE] Preço: 5990.82700, Média: 5990.61366, Volatilidade: 0.000063
2025-09-14 10:51:49.876 -03:00 [INF] [VOLATILIDADE] Preço: 5990.78400, Média: 5990.60336, Volatilidade: 0.000061
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.51
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.59
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: -0.29
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.51
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.51
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.59
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.08 (0.00%)
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.59
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: -0.29
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 10:51:50.589 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 10:51:52.004 -03:00 [INF] [VOLATILIDADE] Preço: 5990.69100, Média: 5990.58808, Volatilidade: 0.000057
2025-09-14 10:51:52.011 -03:00 [INF] Application is shutting down...
2025-09-14 11:05:09.867 -03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-14 11:05:09.888 -03:00 [INF] Hosting environment: Production
2025-09-14 11:05:09.889 -03:00 [INF] Content root path: C:\Users\<USER>\Downloads\excalibur1.5
2025-09-14 11:05:10.117 -03:00 [INF] [CONFIG] Caminho do arquivo de configuração: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 11:05:10.125 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 11:05:10.140 -03:00 [INF] [AUTO-PAUSE] Timer de pausa automática inicializado com intervalo de 30 segundos
2025-09-14 11:05:10.140 -03:00 [INF] Conectando à API Deriv...
2025-09-14 11:05:10.370 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 11:05:10.370 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 11:05:10.370 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 11:05:10.370 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 11:05:10.370 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 11:05:10.370 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 11:05:10.370 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 11:05:10.371 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 11:05:10.371 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 5
2025-09-14 11:05:10.371 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 11:05:10.371 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: False
2025-09-14 11:05:10.371 -03:00 [INF] [DEBUG CanExecuteBuy] TotalProfit: 0.00, MaxLossAmount: 0.00
2025-09-14 11:05:10.371 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 11:05:10.371 -03:00 [INF] [DEBUG CanExecuteBuy] AskPrice: 0
2025-09-14 11:05:10.371 -03:00 [INF] [DEBUG CanExecuteBuy] CurrentProposalId: 'null'
2025-09-14 11:05:10.371 -03:00 [INF] [DEBUG CanExecuteBuy] Normal Mode Result: False
2025-09-14 11:05:10.583 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:47:21
2025-09-14 11:05:10.591 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 11:05:10.591 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 11:05:10.593 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 11:05:10.593 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 11:05:10.595 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 11:05:10.595 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 11:05:10.595 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 11:05:10.596 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 11:05:10.598 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 11:05:10.598 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 11:05:10.598 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 11:05:10.598 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: False
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: null
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: null
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: False
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: null
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 11:05:10.702 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: False (allowing automatic continuation)
2025-09-14 11:05:10.782 -03:00 [INF] Reconexão bem-sucedida: Initial
2025-09-14 11:05:10.902 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 11:05:10.903 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 10:47:21
2025-09-14 11:05:10.966 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 11:05:10.966 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 11:05:10.966 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 11:05:10.966 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 11:05:10.966 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 11:05:10.966 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 11:05:11.174 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:11.174 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 0.00
2025-09-14 11:05:11.174 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 0.00
2025-09-14 11:05:11.174 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 11:05:11.174 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 11:05:11.174 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 11:05:11.174 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.51
2025-09-14 11:05:11.174 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 17713.51
2025-09-14 11:05:11.175 -03:00 [INF] [BALANCE] Initial balance set to: 17713.51
2025-09-14 11:05:11.175 -03:00 [INF] Status de conexão alterado para: True
2025-09-14 11:05:11.178 -03:00 [INF] [CONNECTION] Connection reestablished - restoring application state
2025-09-14 11:05:11.181 -03:00 [INF] [RESTORE] Step 1: Force loading latest configuration
2025-09-14 11:05:11.181 -03:00 [INF] [CONFIG] Carregando configuração do usuário...
2025-09-14 11:05:11.181 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 11:05:10
2025-09-14 11:05:11.181 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 11:05:11.181 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 11:05:11.181 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 11:05:11.181 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 11:05:11.181 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 11:05:11.181 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 11:05:11.181 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 11:05:11.181 -03:00 [INF] [DEBUG] Saindo: SelectedContractType=, SelectedActiveSymbol=, IsCalculating=False
2025-09-14 11:05:11.181 -03:00 [INF] [DUAL_INIT] Modo dual habilitado - SessionProfit: 0.00, TotalProfit: 0.00
2025-09-14 11:05:11.182 -03:00 [INF] [CONFIG] Configuração carregada - Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 11:05:11.182 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 11:05:11.182 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 11:05:11.182 -03:00 [INF] [RESTORE] State to restore - Market: Derived, Symbol: R_10, Contract: CALLE, DualContract: PUTE
2025-09-14 11:05:11.182 -03:00 [INF] [RESTORE] Step 2: Loading active symbols
2025-09-14 11:05:11.182 -03:00 [INF] [DEBUG] LoadActiveSymbolsAsync iniciado
2025-09-14 11:05:11.482 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 11:05:11.482 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 11:05:10
2025-09-14 11:05:11.533 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 11:05:11.533 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 11:05:11.533 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 11:05:11.533 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 11:05:11.533 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 11:05:11.533 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 11:05:11.667 -03:00 [INF] [DEBUG] Recebidos 88 símbolos ativos
2025-09-14 11:05:11.668 -03:00 [INF] [DEBUG] Extraídos 5 mercados únicos
2025-09-14 11:05:11.669 -03:00 [INF] [DEBUG] Adicionado mercado: Commodities
2025-09-14 11:05:11.669 -03:00 [INF] [DEBUG] Adicionado mercado: Cryptocurrencies
2025-09-14 11:05:11.669 -03:00 [INF] [DEBUG] Adicionado mercado: Derived
2025-09-14 11:05:11.669 -03:00 [INF] [DEBUG] Adicionado mercado: Forex
2025-09-14 11:05:11.669 -03:00 [INF] [DEBUG] Adicionado mercado: Stock Indices
2025-09-14 11:05:11.669 -03:00 [INF] [DEBUG] Markets.Count após carregamento: 5
2025-09-14 11:05:11.669 -03:00 [INF] [DEBUG] Markets contém: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 11:05:11.671 -03:00 [INF] [CONFIG] Mercado restaurado: Derived
2025-09-14 11:05:11.671 -03:00 [INF] [RESTORE] Step 3: Restoring selections with retry
2025-09-14 11:05:11.672 -03:00 [INF] [RESTORE RETRY] Attempt 1/5 - Starting restoration
2025-09-14 11:05:11.673 -03:00 [INF] [RESTORE] Starting selection restoration - Market: Derived, Symbol: R_10, Contract: CALLE, Dual: PUTE
2025-09-14 11:05:11.680 -03:00 [INF] [RESTORE] Markets loaded successfully: Commodities, Cryptocurrencies, Derived, Forex, Stock Indices
2025-09-14 11:05:11.681 -03:00 [INF] [RESTORE] ✓ Market restored: 'Derived' -> 'Derived'
2025-09-14 11:05:11.683 -03:00 [INF] [RESTORE] ✓ SubMarket restored: 'Continuous Indices' -> 'Continuous Indices'
2025-09-14 11:05:11.687 -03:00 [INF] [RESTORE] ✓ Symbol restored: 'R_10' -> 'R_10'
2025-09-14 11:05:11.688 -03:00 [INF] [RESTORE] Contract types list empty, waiting... attempt 1/25
2025-09-14 11:05:11.986 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 11:05:11.986 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 11:05:11
2025-09-14 11:05:11.987 -03:00 [INF] [RESTORE] Contract types list empty, waiting... attempt 2/25
2025-09-14 11:05:12.038 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 11:05:12.038 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 11:05:12.038 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 11:05:12.038 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 11:05:12.038 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 11:05:12.038 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 11:05:12.288 -03:00 [INF] [DEBUG] CalculateProposalAsync chamado
2025-09-14 11:05:12.288 -03:00 [INF] [DEBUG] Todos os campos válidos, prosseguindo com cálculo. ContractType=CALLE, Symbol=R_10, Stake=0.35
2025-09-14 11:05:12.293 -03:00 [INF] [RESTORE] ✓ Main contract type restored: 'CALLE' (attempt 3)
2025-09-14 11:05:12.293 -03:00 [INF] [RESTORE] ✓ Dual contract type restored: 'PUTE' (attempt 1)
2025-09-14 11:05:12.293 -03:00 [INF] [RESTORE] Final restoration state:
2025-09-14 11:05:12.293 -03:00 [INF] [RESTORE]   Market: 'Derived' -> 'Derived' ✓
2025-09-14 11:05:12.293 -03:00 [INF] [RESTORE]   SubMarket: 'Continuous Indices' -> 'Continuous Indices' ✓
2025-09-14 11:05:12.293 -03:00 [INF] [RESTORE]   Symbol: 'R_10' -> 'R_10' ✓
2025-09-14 11:05:12.293 -03:00 [INF] [RESTORE]   Contract: 'CALLE' -> 'CALLE' ✓
2025-09-14 11:05:12.293 -03:00 [INF] [RESTORE]   DualContract: 'PUTE' -> 'PUTE' ✓
2025-09-14 11:05:12.294 -03:00 [INF] [TICKS] Subscribed to ticks for symbol: R_10
2025-09-14 11:05:12.593 -03:00 [INF] [CONFIG] Salvando configuração do usuário...
2025-09-14 11:05:12.595 -03:00 [INF] [CONFIG] Configuração carregada com sucesso. Última atualização: 2025-09-14 11:05:11
2025-09-14 11:05:12.647 -03:00 [INF] [CONFIG] Configuração salva com sucesso em: C:\Users\<USER>\AppData\Roaming\Excalibur\excalibur-config.json
2025-09-14 11:05:12.647 -03:00 [INF] [CONFIG] Stake: 0.35, DualTakeProfit: 0.03
2025-09-14 11:05:12.647 -03:00 [INF] [CONFIG] Money Management - MartingaleFactor: 2.00, InitialStake: 0.35, MaxLevel: 14, MaxLoss: 0.01
2025-09-14 11:05:12.647 -03:00 [INF] [CONFIG] Mercado: Derived, Ativo: R_10
2025-09-14 11:05:12.647 -03:00 [INF] [CONFIG] Estratégias - Martingale: False, Dual: True
2025-09-14 11:05:12.648 -03:00 [INF] [CONFIG] Configuração salva com sucesso
2025-09-14 11:05:12.691 -03:00 [INF] [RESTORE VERIFY] Market: True, Symbol: True, Contract: True, DualContract: True
2025-09-14 11:05:12.691 -03:00 [INF] [RESTORE RETRY] ✓ Restoration successful on attempt 1
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 11:05:16.423 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 11:05:16.964 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 11:05:17.176 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 11:05:17.178 -03:00 [INF] [BUY] ExecuteBuyCommand called - IsDualEnabled: True
2025-09-14 11:05:17.178 -03:00 [INF] [BUY] Dual mode detected, calling ExecuteDualEntryCommand
2025-09-14 11:05:17.181 -03:00 [INF] [DUAL DEBUG] 🚀 ExecuteDualEntryCommand started
2025-09-14 11:05:17.181 -03:00 [INF] [DUAL DEBUG] Connection check passed
2025-09-14 11:05:17.181 -03:00 [INF] [DUAL DEBUG] Timing check bypassed for dual mode (no-delay)
2025-09-14 11:05:17.181 -03:00 [INF] [DUAL DEBUG] Validating contract types - ContractType: CALLE, DualContractType: PUTE, Symbol: R_10
2025-09-14 11:05:17.181 -03:00 [INF] [DUAL] Iniciando primeira sessão dual - Session 1
2025-09-14 11:05:17.181 -03:00 [INF] [DUAL] Iniciando entrada dupla - Level 0/5, Session 1/1
2025-09-14 11:05:17.182 -03:00 [INF] [NEW_DUAL] 🚀 Chamando CalculateNewDualStakes...
2025-09-14 11:05:17.183 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 11:05:17.183 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 11:05:17.183 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 11:05:17.183 -03:00 [INF] [NEW_DUAL] ✅ Calculated stakes using new formulas - X: 0.35, Y: 0.60
2025-09-14 11:05:17.183 -03:00 [INF] [NEW_DUAL] 📊 Parameters - Lucro Alvo: 2.00, Alfa: 0.50, Lucro Base: 0.04, R(y/x): 1.70
2025-09-14 11:05:17.183 -03:00 [INF] [NEW_DUAL] 💰 Perdas Acumuladas: 0.00
2025-09-14 11:05:17.184 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: False
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 11:05:17.189 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 11:05:17.399 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=fc7c16b5-571e-ee52-414e-4d65a0799282, AskPrice=0.35, Payout=0.66
2025-09-14 11:05:17.400 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 11:05:17.607 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=9fda5466-67c8-156c-f3c6-f9e75e924256, AskPrice=0.60, Payout=1.16
2025-09-14 11:05:17.607 -03:00 [INF] [NEW_DUAL] Payouts - Contrato 1: 0.66, Contrato 2: 1.16
2025-09-14 11:05:17.607 -03:00 [INF] [NEW_DUAL] Final stakes - Contrato 1: 0.60, Contrato 2: 0.35
2025-09-14 11:05:17.607 -03:00 [INF] [DUAL] Primeira entrada - Random choice: Contrato principal MENOR stake
2025-09-14 11:05:17.607 -03:00 [INF] [DUAL] Stakes atribuídas - Higher: 0.35, Lower: 0.60
2025-09-14 11:05:17.607 -03:00 [INF] [DUAL] Stake MAIOR (0.60) vai para: Lower
2025-09-14 11:05:17.607 -03:00 [INF] [DUAL] Stakes finais - Higher: 0.35, Lower: 0.60
2025-09-14 11:05:17.607 -03:00 [INF] [DUAL] Obtendo proposta para Higher com stake 0.35
2025-09-14 11:05:17.919 -03:00 [INF] [DUAL] Proposta obtida - Higher: ID=fc7c16b5-571e-ee52-414e-4d65a0799282, AskPrice=0.35, Payout=0.66
2025-09-14 11:05:17.919 -03:00 [INF] [DUAL] Obtendo proposta para Lower com stake 0.60
2025-09-14 11:05:18.126 -03:00 [INF] [DUAL] Proposta obtida - Lower: ID=9fda5466-67c8-156c-f3c6-f9e75e924256, AskPrice=0.60, Payout=1.16
2025-09-14 11:05:18.126 -03:00 [INF] [DUAL] Payouts - Contrato 1 (Higher): 0.66, Contrato 2 (Lower): 1.16
2025-09-14 11:05:18.126 -03:00 [INF] [DUAL ENTRY] ===== INICIANDO COMPRAS DUAIS =====
2025-09-14 11:05:18.126 -03:00 [INF] [DUAL ENTRY] Saldo antes das compras: 17713.51
2025-09-14 11:05:18.126 -03:00 [INF] [DUAL ENTRY] Active Exposure antes: 0.00
2025-09-14 11:05:18.126 -03:00 [INF] [DUAL ENTRY] Total stake a ser debitado: 0.95
2025-09-14 11:05:18.126 -03:00 [INF] [DUAL] INTENT BUY -> C1=Higher, Stake=0.35, ProposalId=fc7c16b5-571e-ee52-414e-4d65a0799282
2025-09-14 11:05:18.126 -03:00 [INF] [DUAL] INTENT BUY -> C2=Lower, Stake=0.60, ProposalId=9fda5466-67c8-156c-f3c6-f9e75e924256
2025-09-14 11:05:18.373 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:18.373 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.51
2025-09-14 11:05:18.373 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.51
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.16
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.16
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.51
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.35 (0.00%)
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 11:05:18.374 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:18.380 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.16
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.51
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.56
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.56
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.51
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.95 (0.01%)
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 11:05:18.388 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:18.394 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 11:05:18.394 -03:00 [INF] [DUAL] Ambas as compras executadas com sucesso
2025-09-14 11:05:18.394 -03:00 [INF] [DUAL ENTRY] Saldo após compras: 17713.51
2025-09-14 11:05:18.394 -03:00 [INF] [DUAL ENTRY] Active Exposure após: 0.00
2025-09-14 11:05:18.394 -03:00 [INF] [DUAL ENTRY] Diferença no saldo: 0.00
2025-09-14 11:05:18.394 -03:00 [INF] [DUAL ENTRY] Diferença no Active Exposure: 0.00
2025-09-14 11:05:18.394 -03:00 [INF] [DUAL ENTRY] Contract 1 ID: ************
2025-09-14 11:05:18.394 -03:00 [INF] [DUAL ENTRY] Contract 2 ID: ************
2025-09-14 11:05:18.399 -03:00 [INF] New maximum stake recorded: 0.35
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Contract Type: Higher
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Stake: 0.35
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Payout: 0.66
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Entries count: 0 -> 1
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Saldo: 17713.51 -> 17713.16
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Active Exposure: 0.00 -> 0.35
2025-09-14 11:05:18.399 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.35
2025-09-14 11:05:18.400 -03:00 [INF] New maximum stake recorded: 0.60
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] ===== CONTRATO ADICIONADO À TABELA =====
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Contract ID: ************
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Contract Type: Lower
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Stake: 0.60
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Payout: 1.16
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Entry Price: 
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Session ID: 1
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Is Active: True
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Entries count: 1 -> 2
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Saldo: 17713.16 -> 17712.56
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Active Exposure: 0.35 -> 0.95
2025-09-14 11:05:18.400 -03:00 [INF] [TABLE ADD] Diferença Active Exposure: 0.60
2025-09-14 11:05:18.400 -03:00 [INF] [DUAL] Compra dupla executada no nível 0 (máximo: 5)
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.00, DualMaxLossAmount: 100.00
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 2
2025-09-14 11:05:18.430 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 11:05:19.977 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:19.978 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:19.998 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:19.998 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:19.999 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:20.000 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:20.000 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:20.001 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.56
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17712.56
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.56
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.56
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17712.56
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 11:05:21.406 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:21.935 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:21.936 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:21.936 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:21.936 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:21.944 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:21.944 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:21.944 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:21.945 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.56
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17712.56
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.56
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.56
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17712.56
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.00 (0.00%)
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 11:05:21.966 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.56
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17712.56
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.00
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.00
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.95
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.72
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 1.16
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.72
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17712.56
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 1.16 (0.01%)
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.00
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.00
2025-09-14 11:05:22.672 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.95
2025-09-14 11:05:22.672 -03:00 [WRN] [BALANCE_ALERT] Discrepância significativa detectada!
2025-09-14 11:05:22.672 -03:00 [WRN] [BALANCE_ALERT] Diferença: 1.16 (0.01%)
2025-09-14 11:05:22.672 -03:00 [WRN] [BALANCE_ALERT] Contexto: Account Info Update
2025-09-14 11:05:23.930 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:23.930 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:23.930 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 11:05:23.932 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.35, Corrected=-0.35, Stake=0.35, Payout=0.66
2025-09-14 11:05:23.932 -03:00 [INF] [FALLBACK] Contrato ************ processado às 11:05:23.932 - Profit: -0.35, Win: False, Exit: 5990.9300
2025-09-14 11:05:23.932 -03:00 [INF] [DUAL] Contract result received at 11:05:23.932 - WIN: false
2025-09-14 11:05:23.933 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 2
2025-09-14 11:05:23.933 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 11:05:23.934 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Contract Type: Higher
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Stake: 0.35
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Payout: 0.66
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Entry Price: 5991.1290
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Exit Price: 5990.9300
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Raw Profit: -0.350000
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Rounded Profit: -0.35
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Result: LOSS
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 11:05:23.934 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 11:05:23.934 -03:00 [INF] [BALANCE] Before transaction: 17712.91
2025-09-14 11:05:23.934 -03:00 [INF] Profit Table updated for contract ************: Profit=-0.35, ExitPrice=5990.93, ExitTime=14:13:56
2025-09-14 11:05:23.936 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 11:05:23.936 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 11:05:23.936 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 11:05:23.936 -03:00 [INF] [SESSION PROFIT] Profit do contrato: -0.35
2025-09-14 11:05:23.936 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 11:05:23.936 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 11:05:23.936 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit -0.35
2025-09-14 11:05:23.936 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: False, Contract2Completed: False
2025-09-14 11:05:23.936 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 11:05:23.936 -03:00 [INF] [DUAL DEBUG] Contract index: 0
2025-09-14 11:05:23.936 -03:00 [INF] [DUAL] Contract 1 finished: Stake=0.35, Profit=-0.35
2025-09-14 11:05:23.936 -03:00 [INF] [SESSION PROFIT] Contract 1 - Stake: 0.35, Profit: -0.35
2025-09-14 11:05:23.936 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: True, Contract2Completed: False
2025-09-14 11:05:23.936 -03:00 [INF] [DUAL DEBUG] Waiting for other contract to complete...
2025-09-14 11:05:23.936 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:23.936 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:13:54.000, price=5991.129
2025-09-14 11:05:23.937 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:23.937 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 11:05:23.937 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.56, Corrected=0.56, Stake=0.60, Payout=1.16
2025-09-14 11:05:23.937 -03:00 [INF] [FALLBACK] Contrato ************ processado às 11:05:23.937 - Profit: 0.56, Win: True, Exit: 5990.9300
2025-09-14 11:05:23.937 -03:00 [INF] [DUAL] Contract result received at 11:05:23.937 - WIN: true
2025-09-14 11:05:23.937 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 2
2025-09-14 11:05:23.937 -03:00 [INF] [DUAL] Contracts completed: 0/2
2025-09-14 11:05:23.939 -03:00 [INF] [CONTRACT_STATUS] Contract ************ IsActive set to FALSE - Contract finalized successfully
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] Contract ************ FINISHED
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Contract Type: Lower
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Stake: 0.60
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Payout: 1.16
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Entry Price: 5991.1290
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Exit Price: 5990.9300
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Raw Profit: 0.560000
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Rounded Profit: 0.56
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Result: WIN
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Session ID: 1
2025-09-14 11:05:23.939 -03:00 [INF] [TRANSACTION] - Is Dual Mode: False
2025-09-14 11:05:23.939 -03:00 [INF] [BALANCE] Before transaction: 17713.51
2025-09-14 11:05:23.939 -03:00 [INF] Profit Table updated for contract ************: Profit=0.56, ExitPrice=5990.93, ExitTime=14:13:56
2025-09-14 11:05:23.939 -03:00 [INF] [TOTAL_PROFIT_DEBUG] Modo dual - TotalProfit calculado automaticamente: 0.00 (Completed: 0.00 + Session: 0.00)
2025-09-14 11:05:23.939 -03:00 [INF] [SESSION PROFIT] ===== PROCESSANDO CONTRATO DUAL =====
2025-09-14 11:05:23.939 -03:00 [INF] [SESSION PROFIT] Contract ID: ************
2025-09-14 11:05:23.939 -03:00 [INF] [SESSION PROFIT] Profit do contrato: 0.56
2025-09-14 11:05:23.939 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 11:05:23.939 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 11:05:23.939 -03:00 [INF] [DUAL DEBUG] Processing dual contract ************ with profit 0.56
2025-09-14 11:05:23.939 -03:00 [INF] [DUAL DEBUG] Current state - Contract1Completed: True, Contract2Completed: False
2025-09-14 11:05:23.939 -03:00 [INF] [DUAL DEBUG] Pending contracts: ************, ************
2025-09-14 11:05:23.939 -03:00 [INF] [DUAL DEBUG] Contract index: 1
2025-09-14 11:05:23.939 -03:00 [INF] [DUAL] Contract 2 finished: Stake=0.60, Profit=0.56
2025-09-14 11:05:23.939 -03:00 [INF] [SESSION PROFIT] Contract 2 - Stake: 0.60, Profit: 0.56
2025-09-14 11:05:23.939 -03:00 [INF] [DUAL DEBUG] After processing - Contract1Completed: True, Contract2Completed: True
2025-09-14 11:05:23.939 -03:00 [INF] [DUAL DEBUG] ✅ Both contracts completed in OnContractFinished - calling ProcessDualLevelComplete
2025-09-14 11:05:23.939 -03:00 [INF] [DUAL DEBUG] 🚀 Executing ProcessDualLevelComplete (FAST ASYNC)...
2025-09-14 11:05:23.941 -03:00 [INF] [DUAL] 🎯 ProcessDualLevelComplete STARTED - Level 0, SessionProfit: 0.00
2025-09-14 11:05:23.941 -03:00 [INF] [DUAL REAL RESULT] Primeira entrada (Level 0) - Contract1=-0.35, Contract2=0.56
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] ===== CALCULANDO SESSION PROFIT =====
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] Contract 1 Profit: -0.35
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] Contract 2 Profit: 0.56
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] SessionProfit antes: 0.00
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] TotalProfit antes: 0.00
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] 🎯 Resultado líquido da dupla (pairNet): 0.21
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] 🧮 Cálculo: -0.35 + 0.56 = 0.21
2025-09-14 11:05:23.941 -03:00 [INF] [DUAL_PERDAS] Lucro de 0.21 recuperou 0.00. Perdas restantes: 0.00
2025-09-14 11:05:23.941 -03:00 [INF] [NEW_DUAL] 💰 Perdas acumuladas - Antes: 0.00, Depois: 0.00, Mudança: 0.00
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] Contract 2 ganhou, Contract 1 perdeu. LosingContractTypeIndex = 0
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] SessionProfit atualizado: 0.00 + 0.21 = 0.21
2025-09-14 11:05:23.941 -03:00 [INF] [SESSION PROFIT] Diferença no SessionProfit: 0.21
2025-09-14 11:05:23.941 -03:00 [INF] [DUAL] Pair result: -0.35 + 0.56 = 0.21; SessionProfit (after): 0.21
2025-09-14 11:05:23.941 -03:00 [INF] [DUAL] Updated _previousSessionProfit to: 0.21
2025-09-14 11:05:23.941 -03:00 [INF] [DUAL] SessionProfit calculation completed - avoiding duplicate calculation
2025-09-14 11:05:23.942 -03:00 [INF] [MICRO] Transferência de micro-metas executada. Unit=0.04, TotalProfit=0.20, SessionProfit=0.01
2025-09-14 11:05:23.942 -03:00 [INF] [SESSION_END_DEBUG] Sessão NÃO encerrada - SessionProfit: 0.01, Target: 2.00
2025-09-14 11:05:23.942 -03:00 [INF] [SESSION_END_DEBUG] Diferença para target: 1.99
2025-09-14 11:05:23.942 -03:00 [INF] [SESSION_END_DEBUG] Nível atual: 0/5, Sessão: 1/1
2025-09-14 11:05:23.942 -03:00 [INF] [DUAL] ✅ Entrada dupla #1 completada - Resultado líquido: 0.21
2025-09-14 11:05:23.942 -03:00 [INF] [DUAL] 📈 LUCRO na entrada #1 - Reduzindo perdas acumuladas
2025-09-14 11:05:23.942 -03:00 [INF] [DUAL DEBUG] Continuing within session 1/1 at level 1/5
2025-09-14 11:05:23.942 -03:00 [INF] [DUAL] 🚀 Executing next dual entry FAST PATH... (Level 1/5)
2025-09-14 11:05:23.943 -03:00 [INF] [DUAL_STAKES] 🧮 NOVO CÁLCULO (ancorado na stake menor do campo Stake)
2025-09-14 11:05:23.943 -03:00 [INF] [DUAL_STAKES] Parâmetros: Stake(x)=0.35, Alfa=0.50, Perdas=0.00, Base=0.04, R(y/x)=1.700, Pcap=100.00
2025-09-14 11:05:23.943 -03:00 [INF] [DUAL_STAKES] Resultados: x=0.35, y=0.60, L=0.1787, P=0.2873, k=1.608
2025-09-14 11:05:23.944 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 11:05:23.944
2025-09-14 11:05:23.944 -03:00 [INF] [TIMING] INSTANT POOL BUY: Execução iniciada às 11:05:23.944
2025-09-14 11:05:23.944 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5991.129 at 14:13:54
2025-09-14 11:05:24.160 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 216.5422ms às 11:05:24.160
2025-09-14 11:05:24.160 -03:00 [INF] [TIMING] INSTANT POOL: Proposta obtida em 216.8827ms às 11:05:24.160
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.72
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.71
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.37
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.35
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.37
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.71
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.34 (0.00%)
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 11:05:24.404 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:24.415 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.37
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.71
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17712.77
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: -0.60
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17712.77
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.71
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.94 (0.01%)
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 11:05:24.435 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:24.438 -03:00 [INF] [DEBUG] Contrato comprado: ************, subscrevendo para atualizações
2025-09-14 11:05:25.389 -03:00 [INF] [DUAL DEBUG] ✅ ProcessDualLevelComplete completed
2025-09-14 11:05:26.002 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:26.003 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:26.003 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:26.004 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:27.947 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:27.958 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:27.969 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:27.970 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17712.77
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.71
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.43
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.66
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.43
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.71
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.28 (0.00%)
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 11:05:28.677 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.43
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.71
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.43
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.43
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.71
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.28 (0.00%)
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 11:05:28.724 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:29.950 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:29.950 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 11:05:29.950 -03:00 [INF] [FALLBACK] Contrato ************: API profit=-0.60, Corrected=-0.60, Stake=0.60, Payout=1.16
2025-09-14 11:05:29.950 -03:00 [INF] [FALLBACK] Contrato ************ processado às 11:05:29.950 - Profit: -0.60, Win: False, Exit: 5990.7900
2025-09-14 11:05:29.950 -03:00 [INF] [DUAL] Contract result received at 11:05:29.950 - WIN: false
2025-09-14 11:05:29.950 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 11:05:29.950 -03:00 [INF] [DUAL] Contract result received: LOSS, Pending contracts: 0
2025-09-14 11:05:29.950 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 11:05:29.950 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 11:05:29.950 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 11:05:29.950 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 11:05:29.965 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.849 at 14:14:00
2025-09-14 11:05:29.965 -03:00 [INF] [DEBUG] entry_tick for ************: epoch=********** -> 14:14:00.000, price=5990.849
2025-09-14 11:05:29.966 -03:00 [INF] [TIMING] FALLBACK - Contrato ************ detectado como finalizado
2025-09-14 11:05:29.966 -03:00 [INF] [FALLBACK] Contrato ************: API profit=0.31, Corrected=0.31, Stake=0.35, Payout=0.66
2025-09-14 11:05:29.966 -03:00 [INF] [FALLBACK] Contrato ************ processado às 11:05:29.966 - Profit: 0.31, Win: True, Exit: 5990.7900
2025-09-14 11:05:29.966 -03:00 [INF] [DUAL] Contract result received at 11:05:29.966 - WIN: true
2025-09-14 11:05:29.966 -03:00 [WRN] [DUAL RECOVERY] Contract result received with no pending contracts but active session - possible timeout recovery
2025-09-14 11:05:29.966 -03:00 [INF] [DUAL] Contract result received: WIN, Pending contracts: 0
2025-09-14 11:05:29.966 -03:00 [INF] [DUAL] Contracts completed: 0/0
2025-09-14 11:05:29.966 -03:00 [WRN] [FALLBACK] Contract ************ finished but no active entry found. Creating minimal entry for accounting.
2025-09-14 11:05:29.966 -03:00 [WRN] [CONTRACT_STATUS] PROBLEMA CRÍTICO: Contract ************ não foi encontrado na tabela de profit com IsActive=true!
2025-09-14 11:05:29.966 -03:00 [WRN] [CONTRACT_STATUS] Entradas encontradas para ************: 0
2025-09-14 11:05:29.966 -03:00 [INF] Profit Table entry updated with official entry tick for contract ************: 5990.849 at 14:14:00
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] === INÍCIO VALIDAÇÃO === (Called from: at Excalibur.ViewModels.MainViewModel.CanExecuteBuy() in C:\Users\<USER>\Downloads\excalibur1.5\ViewModels\MainViewModel.cs:line 2713)
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] IsConnected: True
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] IsTradingEnabled: True
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] _userClickedStop: False
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] TradingAllowed (IsTradingEnabled || _userClickedStop || reactivation): True
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedActiveSymbol: R_10
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedContractType: CALLE
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] Stake: 0.35
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] DurationValue: 1
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] DurationUnit: 't'
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] IsDualEnabled: True
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] SessionProfit: 0.01, DualMaxLossAmount: 100.00
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] BaseValidation: True
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] SelectedDualContractType: PUTE
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] _isDualEntryPending: True
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] _pendingDualContracts.Count: 0
2025-09-14 11:05:29.979 -03:00 [INF] [DEBUG CanExecuteBuy] Dual Mode Result: True (allowing automatic continuation)
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.43
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.71
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.43
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.43
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.71
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.28 (0.00%)
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 11:05:31.375 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:31.929 -03:00 [INF] [VOLATILIDADE] Preço: 5991.21300, Média: 5990.98900, Volatilidade: 0.000023
2025-09-14 11:05:33.916 -03:00 [INF] [VOLATILIDADE] Preço: 5991.36600, Média: 5991.02327, Volatilidade: 0.000028
2025-09-14 11:05:35.891 -03:00 [INF] [VOLATILIDADE] Preço: 5991.42900, Média: 5991.05708, Volatilidade: 0.000033
2025-09-14 11:05:38.040 -03:00 [INF] [VOLATILIDADE] Preço: 5991.35000, Média: 5991.07962, Volatilidade: 0.000034
2025-09-14 11:05:39.868 -03:00 [INF] [VOLATILIDADE] Preço: 5991.34900, Média: 5991.09886, Volatilidade: 0.000035
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_UPDATE] Estado antes da atualização:
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_UPDATE] - Balance atual: 17713.43
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_UPDATE] - Balance esperado: 17713.71
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_UPDATE] - SessionProfit: 0.01
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_UPDATE] - CompletedSessionsProfit: 0.20
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_UPDATE] - ActiveExposure: 0.00
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_UPDATE] Novo balance da API: 17713.43
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_UPDATE] Diferença no balance: 0.00
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_VALIDATION] Account Info Update
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_VALIDATION] - API Balance: 17713.43
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_VALIDATION] - Expected Balance: 17713.71
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_VALIDATION] - Discrepancy: 0.28 (0.00%)
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_VALIDATION] - Initial Balance: 17713.51
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_VALIDATION] - Completed Sessions Profit: 0.20
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_VALIDATION] - Session Profit: 0.01
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_VALIDATION] - Active Exposure: 0.00
2025-09-14 11:05:41.372 -03:00 [INF] [BALANCE_VALIDATION] Saldo consistente - diferença aceitável
2025-09-14 11:05:41.885 -03:00 [INF] [VOLATILIDADE] Preço: 5991.60500, Média: 5991.13260, Volatilidade: 0.000040
2025-09-14 11:05:43.903 -03:00 [INF] [VOLATILIDADE] Preço: 5991.58800, Média: 5991.16106, Volatilidade: 0.000043
2025-09-14 11:05:45.018 -03:00 [INF] Application is shutting down...
